// APIX Real-Time Engine Client - Production Ready
// Single WebSocket gateway handling ALL platform events with enterprise features

import { ExtendedAPXEvent } from "./apix-events";

interface APXEvent {
  id: string;
  type: string;
  module:
    | "agents"
    | "tools"
    | "hybrids"
    | "sessions"
    | "approvals"
    | "knowledge"
    | "widgets"
    | "analytics"
    | "billing"
    | "notifications"
    | "providers"
    | "sdk"
    | "sandbox"
    | "system";
  organizationId: string;
  userId: string;
  timestamp: string;
  data: any;
  version: number;
  priority?: "low" | "medium" | "high" | "critical";
  retryCount?: number;
  correlationId?: string;
  causationId?: string;
  metadata?: Record<string, any>;
}

interface APXSubscription {
  id: string;
  module: string;
  eventTypes: string[];
  organizationId: string;
  callback: (event: APXEvent) => void;
  filters?: Record<string, any>;
  priority?: "low" | "medium" | "high" | "critical";
  batchSize?: number;
  throttleMs?: number;
}

interface EventReplayOptions {
  fromTimestamp?: string;
  toTimestamp?: string;
  eventTypes?: string[];
  modules?: string[];
  maxEvents?: number;
  correlationId?: string;
}

interface ConnectionMetrics {
  connectedAt: string;
  reconnectCount: number;
  totalEventsReceived: number;
  totalEventsSent: number;
  avgLatency: number;
  lastHeartbeat: string;
  connectionQuality: "excellent" | "good" | "poor" | "critical";
}

interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: string;
  nextRetryTime: string;
  threshold: number;
}

class APXClient {
  private ws: WebSocket | null = null;
  private subscriptions: Map<string, APXSubscription> = new Map();
  private eventHandlers: Map<string, Set<(event: APXEvent) => void>> =
    new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000;
  private maxReconnectDelay = 30000;
  private organizationId: string;
  private userId: string;
  private token: string;
  private eventBuffer: APXEvent[] = [];
  private maxBufferSize = 10000;
  private isConnected = false;
  private connectionId: string;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private heartbeatTimeout: NodeJS.Timeout | null = null;
  private metrics: ConnectionMetrics;
  private circuitBreaker: CircuitBreakerState;
  private eventStore: Map<string, APXEvent> = new Map(); // For event replay
  private maxEventStoreSize = 50000;
  private compressionEnabled = true;
  private batchingEnabled = true;
  private batchTimeout: NodeJS.Timeout | null = null;
  private pendingBatch: APXEvent[] = [];
  private maxBatchSize = 100;
  private batchTimeoutMs = 100;
  private rateLimiter: Map<string, { count: number; resetTime: number }> =
    new Map();
  private maxEventsPerSecond = 1000;
  private eventListeners: Map<string, Set<(event: APXEvent) => void>> =
    new Map();
  private stateSync: Map<string, any> = new Map(); // Real-time state synchronization
  private pubSubChannels: Map<string, Set<string>> = new Map(); // Cross-module pub/sub

  constructor(organizationId: string, userId: string, token: string) {
    this.organizationId = organizationId;
    this.userId = userId;
    this.token = token;
    this.connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.metrics = {
      connectedAt: "",
      reconnectCount: 0,
      totalEventsReceived: 0,
      totalEventsSent: 0,
      avgLatency: 0,
      lastHeartbeat: "",
      connectionQuality: "excellent",
    };

    this.circuitBreaker = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: "",
      nextRetryTime: "",
      threshold: 5,
    };

    // Initialize rate limiter
    this.resetRateLimit();
    setInterval(() => this.resetRateLimit(), 1000);
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (
        this.circuitBreaker.isOpen &&
        Date.now() < new Date(this.circuitBreaker.nextRetryTime).getTime()
      ) {
        reject(
          new Error(
            "Circuit breaker is open. Connection blocked until retry time.",
          ),
        );
        return;
      }

      try {
        const wsUrl = this.buildWebSocketUrl();
        this.ws = new WebSocket(wsUrl);

        // Set connection timeout
        const connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            this.handleConnectionFailure();
            reject(new Error("Connection timeout"));
          }
        }, 10000);

        this.ws.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log(`APIX WebSocket connected [${this.connectionId}]`);
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.metrics.connectedAt = new Date().toISOString();
          this.circuitBreaker.isOpen = false;
          this.circuitBreaker.failureCount = 0;

          this.startHeartbeat();
          this.flushEventBuffer();
          this.resubscribeAll();

          // Send connection event
          this.sendEvent({
            id: `conn_${this.connectionId}`,
            type: "connection_established",
            module: "system",
            organizationId: this.organizationId,
            userId: this.userId,
            timestamp: new Date().toISOString(),
            data: {
              connectionId: this.connectionId,
              clientVersion: "2.0.0",
              capabilities: ["compression", "batching", "replay", "pubsub"],
            },
            version: 1,
          });

          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleIncomingMessage(event);
        };

        this.ws.onclose = (event) => {
          clearTimeout(connectionTimeout);
          console.log(
            `APIX WebSocket disconnected [${this.connectionId}] - Code: ${event.code}, Reason: ${event.reason}`,
          );
          this.isConnected = false;
          this.stopHeartbeat();

          if (event.code !== 1000) {
            // Not a normal closure
            this.handleConnectionFailure();
          }

          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          clearTimeout(connectionTimeout);
          console.error(`APIX WebSocket error [${this.connectionId}]:`, error);
          this.handleConnectionFailure();
          reject(error);
        };
      } catch (error) {
        this.handleConnectionFailure();
        reject(error);
      }
    });
  }

  private buildWebSocketUrl(): string {
    const baseUrl =
      process.env.NEXT_PUBLIC_APIX_WS_URL || "wss://api.synapseai.com/apix";
    const params = new URLSearchParams({
      org: this.organizationId,
      token: this.token,
      conn: this.connectionId,
      version: "2.0.0",
      compression: this.compressionEnabled.toString(),
      batching: this.batchingEnabled.toString(),
    });
    return `${baseUrl}?${params.toString()}`;
  }

  private handleIncomingMessage(event: MessageEvent): void {
    try {
      let data = event.data;

      // Handle compression if enabled
      if (
        this.compressionEnabled &&
        typeof data === "string" &&
        data.startsWith("compressed:")
      ) {
        data = this.decompress(data.substring(11));
      }

      const message = JSON.parse(data);

      // Handle different message types
      if (message.type === "batch") {
        message.events.forEach((apxEvent: APXEvent) => {
          this.processEvent(apxEvent);
        });
      } else if (message.type === "heartbeat") {
        this.handleHeartbeat(message);
      } else if (message.type === "state_sync") {
        this.handleStateSync(message);
      } else {
        this.processEvent(message as APXEvent);
      }

      this.metrics.totalEventsReceived++;
      this.updateConnectionQuality();
    } catch (error) {
      console.error("Error processing incoming message:", error);
    }
  }

  private processEvent(apxEvent: APXEvent): void {
    // Store event for replay capability
    this.storeEvent(apxEvent);

    // Update metrics
    if (apxEvent.metadata?.sentAt) {
      const latency = Date.now() - new Date(apxEvent.metadata.sentAt).getTime();
      this.updateLatencyMetrics(latency);
    }

    // Handle the event
    this.handleEvent(apxEvent);
  }

  private handleConnectionFailure(): void {
    this.circuitBreaker.failureCount++;
    this.circuitBreaker.lastFailureTime = new Date().toISOString();

    if (this.circuitBreaker.failureCount >= this.circuitBreaker.threshold) {
      this.circuitBreaker.isOpen = true;
      this.circuitBreaker.nextRetryTime = new Date(
        Date.now() + 60000,
      ).toISOString(); // 1 minute
      console.warn("Circuit breaker opened due to repeated failures");
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws) {
        const heartbeat = {
          type: "heartbeat",
          timestamp: new Date().toISOString(),
          connectionId: this.connectionId,
        };
        this.ws.send(JSON.stringify(heartbeat));

        // Set timeout for heartbeat response
        this.heartbeatTimeout = setTimeout(() => {
          console.warn("Heartbeat timeout - connection may be stale");
          this.metrics.connectionQuality = "poor";
        }, 5000);
      }
    }, 30000); // Every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  private handleHeartbeat(message: any): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
    this.metrics.lastHeartbeat = new Date().toISOString();
    this.metrics.connectionQuality = "excellent";
  }

  private updateLatencyMetrics(latency: number): void {
    // Simple moving average
    this.metrics.avgLatency = this.metrics.avgLatency * 0.9 + latency * 0.1;
  }

  private updateConnectionQuality(): void {
    const now = Date.now();
    const lastHeartbeat = new Date(this.metrics.lastHeartbeat).getTime();
    const timeSinceHeartbeat = now - lastHeartbeat;

    if (timeSinceHeartbeat > 60000) {
      this.metrics.connectionQuality = "critical";
    } else if (timeSinceHeartbeat > 45000 || this.metrics.avgLatency > 1000) {
      this.metrics.connectionQuality = "poor";
    } else if (this.metrics.avgLatency > 500) {
      this.metrics.connectionQuality = "good";
    } else {
      this.metrics.connectionQuality = "excellent";
    }
  }

  private attemptReconnect(): void {
    if (this.circuitBreaker.isOpen) {
      console.log("Circuit breaker is open, skipping reconnect attempt");
      return;
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      this.metrics.reconnectCount++;

      // Exponential backoff with jitter
      const baseDelay = Math.min(
        this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
        this.maxReconnectDelay,
      );
      const jitter = Math.random() * 1000;
      const delay = baseDelay + jitter;

      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${Math.round(delay)}ms`,
      );

      setTimeout(() => {
        this.connect().catch((error) => {
          console.error("Reconnection failed:", error);
          if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error(
              "Max reconnection attempts reached. Connection permanently failed.",
            );
            this.emit("connection_failed", {
              reason: "max_attempts_reached",
              attempts: this.reconnectAttempts,
            });
          }
        });
      }, delay);
    } else {
      console.error("Max reconnection attempts reached");
      this.emit("connection_failed", {
        reason: "max_attempts_reached",
        attempts: this.reconnectAttempts,
      });
    }
  }

  private resubscribeAll(): void {
    console.log(`Resubscribing to ${this.subscriptions.size} subscriptions`);
    this.subscriptions.forEach((subscription) => {
      this.sendSubscriptionRequest(subscription);
    });
  }

  private sendSubscriptionRequest(subscription: APXSubscription): void {
    if (this.isConnected && this.ws) {
      const subscribeEvent = {
        id: `sub_${subscription.id}`,
        type: "subscribe",
        module: "system",
        organizationId: this.organizationId,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        data: {
          subscriptionId: subscription.id,
          module: subscription.module,
          eventTypes: subscription.eventTypes,
          filters: subscription.filters,
          priority: subscription.priority,
          batchSize: subscription.batchSize,
          throttleMs: subscription.throttleMs,
        },
        version: 1,
      };
      this.ws.send(JSON.stringify(subscribeEvent));
    }
  }

  private handleEvent(event: APXEvent): void {
    // Multi-tenant security: Organization-scoped event filtering
    if (event.organizationId !== this.organizationId) {
      console.warn(
        `Received event for different organization: ${event.organizationId}`,
      );
      return; // Strict organization isolation
    }

    // Rate limiting check
    if (!this.checkRateLimit(event.type)) {
      console.warn(`Rate limit exceeded for event type: ${event.type}`);
      return;
    }

    // Route event to subscribed handlers with error handling
    this.subscriptions.forEach((subscription) => {
      if (this.shouldHandleEvent(subscription, event)) {
        try {
          // Apply filters if specified
          if (
            subscription.filters &&
            !this.matchesFilters(event, subscription.filters)
          ) {
            return;
          }

          // Handle throttling
          if (subscription.throttleMs) {
            this.throttleCallback(subscription, event);
          } else {
            subscription.callback(event);
          }
        } catch (error) {
          console.error(
            `Error in event handler for subscription ${subscription.id}:`,
            error,
          );
          // Emit error event for monitoring
          this.emit("handler_error", {
            subscriptionId: subscription.id,
            eventId: event.id,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }
    });

    // Emit to generic event listeners
    const listeners = this.eventListeners.get(event.type) || new Set();
    listeners.forEach((listener) => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Error in event listener for ${event.type}:`, error);
      }
    });

    // Handle pub/sub routing
    this.routePubSubEvent(event);

    // Update state synchronization
    this.updateStateSync(event);
  }

  private shouldHandleEvent(
    subscription: APXSubscription,
    event: APXEvent,
  ): boolean {
    return (
      (subscription.module === event.module &&
        subscription.eventTypes.includes(event.type)) ||
      subscription.module === "*" || // Wildcard module subscription
      subscription.eventTypes.includes("*") // Wildcard event type subscription
    );
  }

  private matchesFilters(
    event: APXEvent,
    filters: Record<string, any>,
  ): boolean {
    return Object.entries(filters).every(([key, value]) => {
      const eventValue = this.getNestedValue(event, key);
      if (Array.isArray(value)) {
        return value.includes(eventValue);
      }
      return eventValue === value;
    });
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  private throttleCallback(
    subscription: APXSubscription,
    event: APXEvent,
  ): void {
    const throttleKey = `${subscription.id}_throttle`;
    const now = Date.now();
    const lastCall = this.rateLimiter.get(throttleKey)?.resetTime || 0;

    if (now - lastCall >= (subscription.throttleMs || 0)) {
      subscription.callback(event);
      this.rateLimiter.set(throttleKey, { count: 1, resetTime: now });
    }
  }

  private routePubSubEvent(event: APXEvent): void {
    // Cross-module pub/sub routing
    const channelKey = `${event.module}:${event.type}`;
    const subscribers = this.pubSubChannels.get(channelKey) || new Set();

    subscribers.forEach((subscriberId) => {
      const subscription = this.subscriptions.get(subscriberId);
      if (
        subscription &&
        subscription.organizationId === event.organizationId
      ) {
        try {
          subscription.callback(event);
        } catch (error) {
          console.error(`Error in pub/sub handler for ${subscriberId}:`, error);
        }
      }
    });
  }

  private updateStateSync(event: APXEvent): void {
    // Real-time state synchronization
    if (
      event.type.endsWith("_updated") ||
      event.type.endsWith("_created") ||
      event.type.endsWith("_deleted")
    ) {
      const stateKey = `${event.module}:${event.data?.id || event.id}`;

      if (event.type.endsWith("_deleted")) {
        this.stateSync.delete(stateKey);
      } else {
        this.stateSync.set(stateKey, {
          ...event.data,
          _lastUpdated: event.timestamp,
          _version: event.version,
        });
      }

      // Emit state change event
      this.emit("state_changed", {
        key: stateKey,
        action: event.type.split("_").pop(),
        data: event.data,
        timestamp: event.timestamp,
      });
    }
  }

  private checkRateLimit(eventType: string): boolean {
    const now = Date.now();
    const windowStart = Math.floor(now / 1000) * 1000; // 1-second window
    const key = `${eventType}_${windowStart}`;

    const current = this.rateLimiter.get(key) || {
      count: 0,
      resetTime: windowStart,
    };

    if (current.count >= this.maxEventsPerSecond) {
      return false;
    }

    this.rateLimiter.set(key, {
      count: current.count + 1,
      resetTime: current.resetTime,
    });

    return true;
  }

  private resetRateLimit(): void {
    const now = Date.now();
    const cutoff = now - 2000; // Keep last 2 seconds

    for (const [key, value] of Array.from(this.rateLimiter.entries())) {
      if (value.resetTime < cutoff) {
        this.rateLimiter.delete(key);
      }
    }
  }

  private flushEventBuffer(): void {
    if (this.eventBuffer.length === 0) return;

    console.log(`Flushing ${this.eventBuffer.length} buffered events`);

    if (this.batchingEnabled && this.eventBuffer.length > 1) {
      // Send as batch
      const batch = {
        type: "batch",
        events: this.eventBuffer.splice(
          0,
          Math.min(this.eventBuffer.length, this.maxBatchSize),
        ),
        timestamp: new Date().toISOString(),
        connectionId: this.connectionId,
      };

      if (this.ws && this.isConnected) {
        this.ws.send(JSON.stringify(batch));
        this.metrics.totalEventsSent += batch.events.length;
      }
    } else {
      // Send individually
      while (this.eventBuffer.length > 0) {
        const event = this.eventBuffer.shift();
        if (event) {
          this.sendEvent(event);
        }
      }
    }
  }

  private storeEvent(event: APXEvent): void {
    // Store for event replay capability
    this.eventStore.set(event.id, event);

    // Maintain store size limit
    if (this.eventStore.size > this.maxEventStoreSize) {
      const oldestKey = this.eventStore.keys().next().value;
      if (oldestKey) {
        this.eventStore.delete(oldestKey);
      }
    }
  }

  private compress(data: string): string {
    // Simple compression simulation - in production use actual compression
    return btoa(data);
  }

  private decompress(data: string): string {
    // Simple decompression simulation - in production use actual decompression
    return atob(data);
  }

  private emit(eventType: string, data: any): void {
    // Internal event emission for monitoring and debugging
    const internalEvent = {
      type: eventType,
      data,
      timestamp: new Date().toISOString(),
      connectionId: this.connectionId,
    };

    // In production, this would integrate with monitoring systems
    console.debug("APIX Internal Event:", internalEvent);
  }

  subscribe(
    module: string,
    eventTypes: string[],
    callback: (event: APXEvent) => void,
    options?: {
      filters?: Record<string, any>;
      priority?: "low" | "medium" | "high" | "critical";
      batchSize?: number;
      throttleMs?: number;
    },
  ): string {
    const subscriptionId = `${module}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const subscription: APXSubscription = {
      id: subscriptionId,
      module,
      eventTypes,
      organizationId: this.organizationId,
      callback,
      filters: options?.filters,
      priority: options?.priority || "medium",
      batchSize: options?.batchSize,
      throttleMs: options?.throttleMs,
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Send subscription request to server
    if (this.isConnected) {
      this.sendSubscriptionRequest(subscription);
    }

    console.log(
      `Subscribed to ${module}:${eventTypes.join(",")} [${subscriptionId}]`,
    );
    return subscriptionId;
  }

  // Enhanced subscription methods
  subscribeToModule(
    module: string,
    callback: (event: APXEvent) => void,
  ): string {
    return this.subscribe(module, ["*"], callback);
  }

  subscribeToEventType(
    eventType: string,
    callback: (event: APXEvent) => void,
  ): string {
    return this.subscribe("*", [eventType], callback);
  }

  subscribeWithFilter(
    module: string,
    eventTypes: string[],
    filters: Record<string, any>,
    callback: (event: APXEvent) => void,
  ): string {
    return this.subscribe(module, eventTypes, callback, { filters });
  }

  // Pub/Sub channel subscription
  subscribeToPubSubChannel(
    channel: string,
    callback: (event: APXEvent) => void,
  ): string {
    const subscriptionId = `pubsub_${channel}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    if (!this.pubSubChannels.has(channel)) {
      this.pubSubChannels.set(channel, new Set());
    }

    this.pubSubChannels.get(channel)!.add(subscriptionId);

    // Store callback for this subscription
    const subscription: APXSubscription = {
      id: subscriptionId,
      module: channel.split(":")[0] || "*",
      eventTypes: [channel.split(":")[1] || "*"],
      organizationId: this.organizationId,
      callback,
    };

    this.subscriptions.set(subscriptionId, subscription);

    return subscriptionId;
  }

  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      this.subscriptions.delete(subscriptionId);

      // Remove from pub/sub channels
      this.pubSubChannels.forEach((subscribers, channel) => {
        subscribers.delete(subscriptionId);
        if (subscribers.size === 0) {
          this.pubSubChannels.delete(channel);
        }
      });

      // Send unsubscribe request to server
      if (this.isConnected) {
        this.sendEvent({
          id: `unsub_${subscriptionId}`,
          type: "unsubscribe",
          module: "system",
          organizationId: this.organizationId,
          userId: this.userId,
          timestamp: new Date().toISOString(),
          data: { subscriptionId },
          version: 1,
        });
      }

      console.log(`Unsubscribed from ${subscriptionId}`);
    }
  }

  unsubscribeAll(): void {
    const subscriptionIds = Array.from(this.subscriptions.keys());
    subscriptionIds.forEach((id) => this.unsubscribe(id));
    console.log(
      `Unsubscribed from all ${subscriptionIds.length} subscriptions`,
    );
  }

  sendEvent(event: APXEvent): void {
    // Add metadata
    event.metadata = {
      ...event.metadata,
      sentAt: new Date().toISOString(),
      connectionId: this.connectionId,
      clientVersion: "2.0.0",
    };

    if (this.isConnected && this.ws) {
      try {
        let payload = JSON.stringify(event);

        // Apply compression if enabled and payload is large
        if (this.compressionEnabled && payload.length > 1024) {
          payload = "compressed:" + this.compress(payload);
        }

        this.ws.send(payload);
        this.metrics.totalEventsSent++;
      } catch (error) {
        console.error("Error sending event:", error);
        this.bufferEvent(event);
      }
    } else {
      this.bufferEvent(event);
    }
  }

  private bufferEvent(event: APXEvent): void {
    // Buffer events when disconnected with size limit
    if (this.eventBuffer.length >= this.maxBufferSize) {
      // Remove oldest events to make room
      const removed = this.eventBuffer.splice(
        0,
        Math.floor(this.maxBufferSize * 0.1),
      );
      console.warn(
        `Event buffer full, removed ${removed.length} oldest events`,
      );
    }

    this.eventBuffer.push(event);
  }

  // Batch event sending
  sendEventBatch(events: APXEvent[]): void {
    if (!this.batchingEnabled) {
      events.forEach((event) => this.sendEvent(event));
      return;
    }

    const batch = {
      type: "batch",
      events: events.slice(0, this.maxBatchSize),
      timestamp: new Date().toISOString(),
      connectionId: this.connectionId,
    };

    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(batch));
      this.metrics.totalEventsSent += batch.events.length;
    } else {
      events.forEach((event) => this.bufferEvent(event));
    }

    // Handle remaining events if batch was too large
    if (events.length > this.maxBatchSize) {
      this.sendEventBatch(events.slice(this.maxBatchSize));
    }
  }

  // Event replay functionality
  async replayEvents(options: EventReplayOptions = {}): Promise<APXEvent[]> {
    const replayRequest = {
      id: `replay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: "event_replay_request",
      module: "system",
      organizationId: this.organizationId,
      userId: this.userId,
      timestamp: new Date().toISOString(),
      data: options,
      version: 1,
    };

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error("Event replay request timeout"));
      }, 30000);

      const replaySubscription = this.subscribe(
        "system",
        ["event_replay_response"],
        (event) => {
          if (event.data.requestId === replayRequest.id) {
            clearTimeout(timeoutId);
            this.unsubscribe(replaySubscription);
            resolve(event.data.events || []);
          }
        },
      );

      this.sendEvent(replayRequest as APXEvent);
    });
  }

  // State synchronization methods
  getState(key: string): any {
    return this.stateSync.get(key);
  }

  getAllState(): Map<string, any> {
    return new Map(this.stateSync);
  }

  syncState(key: string, data: any): void {
    this.stateSync.set(key, data);

    // Send state sync event
    this.sendEvent({
      id: `state_sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: "state_sync",
      module: "system",
      organizationId: this.organizationId,
      userId: this.userId,
      timestamp: new Date().toISOString(),
      data: { key, data },
      version: 1,
    });
  }

  private handleStateSync(message: any): void {
    if (message.data?.key && message.data?.data !== undefined) {
      this.stateSync.set(message.data.key, message.data.data);
      this.emit("state_synced", message.data);
    }
  }

  disconnect(): void {
    console.log(`Disconnecting APIX client [${this.connectionId}]`);

    // Send disconnect event
    if (this.isConnected && this.ws) {
      this.sendEvent({
        id: `disconnect_${this.connectionId}`,
        type: "connection_closing",
        module: "system",
        organizationId: this.organizationId,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        data: {
          connectionId: this.connectionId,
          reason: "client_disconnect",
          metrics: this.metrics,
        },
        version: 1,
      });
    }

    // Clean up resources
    this.stopHeartbeat();

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    if (this.ws) {
      this.ws.close(1000, "Client disconnect");
      this.ws = null;
    }

    this.subscriptions.clear();
    this.eventListeners.clear();
    this.pubSubChannels.clear();
    this.eventBuffer.length = 0;
    this.stateSync.clear();
    this.isConnected = false;

    console.log("APIX client disconnected and cleaned up");
  }

  // Connection and performance monitoring
  getMetrics(): ConnectionMetrics {
    return { ...this.metrics };
  }

  getCircuitBreakerState(): CircuitBreakerState {
    return { ...this.circuitBreaker };
  }

  getConnectionInfo(): {
    isConnected: boolean;
    connectionId: string;
    organizationId: string;
    userId: string;
    subscriptionCount: number;
    bufferedEventCount: number;
    stateCount: number;
  } {
    return {
      isConnected: this.isConnected,
      connectionId: this.connectionId,
      organizationId: this.organizationId,
      userId: this.userId,
      subscriptionCount: this.subscriptions.size,
      bufferedEventCount: this.eventBuffer.length,
      stateCount: this.stateSync.size,
    };
  }

  // Event listener management (for internal events)
  addEventListener(
    eventType: string,
    listener: (event: APXEvent) => void,
  ): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
  }

  removeEventListener(
    eventType: string,
    listener: (event: APXEvent) => void,
  ): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
  }

  // Health check
  async healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    details: Record<string, any>;
  }> {
    const now = Date.now();
    const lastHeartbeat = new Date(this.metrics.lastHeartbeat).getTime();
    const timeSinceHeartbeat = now - lastHeartbeat;

    let status: "healthy" | "degraded" | "unhealthy" = "healthy";

    if (!this.isConnected || this.circuitBreaker.isOpen) {
      status = "unhealthy";
    } else if (
      timeSinceHeartbeat > 60000 ||
      this.metrics.connectionQuality === "poor"
    ) {
      status = "degraded";
    }

    return {
      status,
      details: {
        connected: this.isConnected,
        connectionQuality: this.metrics.connectionQuality,
        timeSinceHeartbeat,
        circuitBreakerOpen: this.circuitBreaker.isOpen,
        bufferedEvents: this.eventBuffer.length,
        subscriptions: this.subscriptions.size,
        avgLatency: this.metrics.avgLatency,
        reconnectCount: this.metrics.reconnectCount,
      },
    };
  }
}


// Multi-tenant client management
const apixClients = new Map<string, APXClient>();

export function getAPXClient(
  organizationId?: string,
  userId?: string,
  token?: string,
): APXClient {
  if (!organizationId || !userId || !token) {
    throw new Error(
      "APIX client requires organizationId, userId, and token for multi-tenant security.",
    );
  }

  const clientKey = `${organizationId}:${userId}`;

  if (!apixClients.has(clientKey)) {
    const client = new APXClient(organizationId, userId, token);
    apixClients.set(clientKey, client);

    // Auto-connect
    client.connect().catch((error) => {
      console.error(
        `Failed to auto-connect APIX client for ${clientKey}:`,
        error,
      );
    });
  }

  return apixClients.get(clientKey)!;
}

// Cleanup function for client management
export function disconnectAPXClient(
  organizationId: string,
  userId: string,
): void {
  const clientKey = `${organizationId}:${userId}`;
  const client = apixClients.get(clientKey);

  if (client) {
    client.disconnect();
    apixClients.delete(clientKey);
  }
}

// Get all active clients (for monitoring)
export function getActiveAPXClients(): Array<{
  organizationId: string;
  userId: string;
  connectionInfo: ReturnType<APXClient["getConnectionInfo"]>;
}> {
  return Array.from(apixClients.entries()).map(([key, client]) => {
    const [organizationId, userId] = key.split(":");
    return {
      organizationId,
      userId,
      connectionInfo: client.getConnectionInfo(),
    };
  });
}

// Cleanup all clients (for app shutdown)
export function disconnectAllAPXClients(): void {
  apixClients.forEach((client, key) => {
    console.log(`Disconnecting APIX client: ${key}`);
    client.disconnect();
  });
  apixClients.clear();
}

export {
  APXClient,
  type APXEvent,
  type APXSubscription,
  type EventReplayOptions,
  type ConnectionMetrics,
  type CircuitBreakerState,
};
