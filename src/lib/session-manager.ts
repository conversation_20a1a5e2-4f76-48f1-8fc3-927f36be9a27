// Session + Memory Management
// Redis-based unified session storage with cross-module sharing and intelligent memory management

interface SessionData {
  id: string;
  organizationId: string;
  userId: string;
  module: string;
  type: "agent" | "tool" | "hybrid" | "user" | "system";
  data: any;
  metadata: {
    createdAt: string;
    updatedAt: string;
    lastAccessedAt: string;
    expiresAt?: string;
    memoryUsage: number;
    priority: "low" | "medium" | "high" | "critical";
  };
  context: {
    conversationId?: string;
    workflowId?: string;
    parentSessionId?: string; 
    tags: string[];
  };
}

interface MemoryLimits {
  maxSessionSize: number; // bytes
  maxTotalMemory: number; // bytes
  maxSessionAge: number; // milliseconds
  maxIdleTime: number; // milliseconds
}

interface SessionAnalytics {
  totalSessions: number;
  activeSessions: number;
  memoryUsage: number;
  avgSessionSize: number;
  avgSessionAge: number;
  topModules: Array<{ module: string; count: number; memory: number }>;
}

class SessionManager {
  static getSessionId(module: string, type: string): string {
    return `${module}_${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sessions: Map<string, SessionData> = new Map();
  private memoryLimits: MemoryLimits;
  private organizationId: string;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(organizationId: string, limits?: Partial<MemoryLimits>) {
    this.organizationId = organizationId;
    this.memoryLimits = {
      maxSessionSize: 10 * 1024 * 1024, // 10MB per session
      maxTotalMemory: 100 * 1024 * 1024, // 100MB total
      maxSessionAge: 24 * 60 * 60 * 1000, // 24 hours
      maxIdleTime: 2 * 60 * 60 * 1000, // 2 hours
      ...limits,
    };

    // Start cleanup process
    this.startCleanupProcess();
  }

  async createSession(
    module: string,
    type: SessionData["type"],
    data: any,
    context?: Partial<SessionData["context"]>,
  ): Promise<string> {
    const sessionId = SessionManager.getSessionId(module, type);
    const now = new Date().toISOString();
    const dataSize = this.calculateDataSize(data);

    // Check memory limits
    if (dataSize > this.memoryLimits.maxSessionSize) {
      throw new Error(
        `Session data exceeds maximum size limit (${this.memoryLimits.maxSessionSize} bytes)`,
      );
    }

    const session: SessionData = {
      id: sessionId,
      organizationId: this.organizationId,
      userId: context?.parentSessionId || "system",
      module,
      type,
      data,
      metadata: {
        createdAt: now,
        updatedAt: now,
        lastAccessedAt: now,
        memoryUsage: dataSize,
        priority: "medium",
      },
      context: {
        tags: [],
        ...context,
      },
    };

    // Enforce total memory limit
    await this.enforceMemoryLimits(dataSize);

    this.sessions.set(sessionId, session);

    // Persist to Redis (simulated)
    await this.persistSession(session);

    return sessionId;
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    let session = this.sessions.get(sessionId);

    if (!session) {
      // Try to load from Redis (simulated)
      session = await this.loadSession(sessionId);
      if (session) {
        this.sessions.set(sessionId, session);
      }
    }

    if (session) {
      // Update last accessed time
      session.metadata.lastAccessedAt = new Date().toISOString();
      await this.persistSession(session);
    }

    return session || null;
  }

  async updateSession(
    sessionId: string,
    data: any,
    merge: boolean = true,
  ): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const newData = merge ? { ...session.data, ...data } : data;
    const newSize = this.calculateDataSize(newData);

    if (newSize > this.memoryLimits.maxSessionSize) {
      // Attempt intelligent truncation
      const truncatedData = await this.intelligentTruncation(
        newData,
        this.memoryLimits.maxSessionSize,
      );
      session.data = truncatedData;
      session.metadata.memoryUsage = this.calculateDataSize(truncatedData);
    } else {
      session.data = newData;
      session.metadata.memoryUsage = newSize;
    }

    session.metadata.updatedAt = new Date().toISOString();
    await this.persistSession(session);
  }

  async deleteSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
    // Delete from Redis (simulated)
    await this.deletePersistedSession(sessionId);
  }

  async getSessionsByModule(module: string): Promise<SessionData[]> {
    const sessions: SessionData[] = [];

    for (const session of Array.from(this.sessions.values())) {
      if (
        session.module === module &&
        session.organizationId === this.organizationId
      ) {
        sessions.push(session);
      }
    }

    return sessions;
  }

  async getSessionAnalytics(): Promise<SessionAnalytics> {
    const sessions = Array.from(this.sessions.values()).filter(
      (s) => s.organizationId === this.organizationId,
    );

    const totalMemory = sessions.reduce(
      (sum, s) => sum + s.metadata.memoryUsage,
      0,
    );
    const moduleStats = new Map<string, { count: number; memory: number }>();

    sessions.forEach((session) => {
      const stats = moduleStats.get(session.module) || { count: 0, memory: 0 };
      stats.count++;
      stats.memory += session.metadata.memoryUsage;
      moduleStats.set(session.module, stats);
    });

    const topModules = Array.from(moduleStats.entries())
      .map(([module, stats]) => ({ module, ...stats }))
      .sort((a, b) => b.memory - a.memory)
      .slice(0, 10);

    return {
      totalSessions: sessions.length,
      activeSessions: sessions.filter((s) => this.isSessionActive(s)).length,
      memoryUsage: totalMemory,
      avgSessionSize: sessions.length > 0 ? totalMemory / sessions.length : 0,
      avgSessionAge:
        sessions.length > 0
          ? sessions.reduce(
              (sum, s) =>
                sum + (Date.now() - new Date(s.metadata.createdAt).getTime()),
              0,
            ) / sessions.length
          : 0,
      topModules,
    };
  }

  private async enforceMemoryLimits(newDataSize: number): Promise<void> {
    const currentMemory = Array.from(this.sessions.values())
      .filter((s) => s.organizationId === this.organizationId)
      .reduce((sum, s) => sum + s.metadata.memoryUsage, 0);

    if (currentMemory + newDataSize > this.memoryLimits.maxTotalMemory) {
      // Remove sessions based on priority and age
      const sessions = Array.from(this.sessions.values())
        .filter((s) => s.organizationId === this.organizationId)
        .sort((a, b) => {
          // Sort by priority (low first) then by last accessed time (oldest first)
          const priorityOrder = { low: 0, medium: 1, high: 2, critical: 3 };
          const priorityDiff =
            priorityOrder[a.metadata.priority] -
            priorityOrder[b.metadata.priority];
          if (priorityDiff !== 0) return priorityDiff;

          return (
            new Date(a.metadata.lastAccessedAt).getTime() -
            new Date(b.metadata.lastAccessedAt).getTime()
          );
        });

      let memoryFreed = 0;
      for (const session of sessions) {
        if (memoryFreed >= newDataSize) break;
        if (session.metadata.priority !== "critical") {
          memoryFreed += session.metadata.memoryUsage;
          await this.deleteSession(session.id);
        }
      }
    }
  }

  private async intelligentTruncation(
    data: any,
    maxSize: number,
  ): Promise<any> {
    // Simple truncation strategy - in production, this would be more sophisticated
    const dataStr = JSON.stringify(data);
    if (dataStr.length <= maxSize) return data;

    // Try to preserve important fields
    const truncated = { ...data };

    // Remove or truncate large arrays/strings
    Object.keys(truncated).forEach((key) => {
      if (Array.isArray(truncated[key]) && truncated[key].length > 100) {
        truncated[key] = truncated[key].slice(0, 100);
        truncated[`${key}_truncated`] = true;
      } else if (
        typeof truncated[key] === "string" &&
        truncated[key].length > 1000
      ) {
        truncated[key] = truncated[key].substring(0, 1000) + "...";
        truncated[`${key}_truncated`] = true;
      }
    });

    return truncated;
  }

  private calculateDataSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  private isSessionActive(session: SessionData): boolean {
    const now = Date.now();
    const lastAccessed = new Date(session.metadata.lastAccessedAt).getTime();
    const idleTime = now - lastAccessed;

    return idleTime < this.memoryLimits.maxIdleTime;
  }

  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(
      async () => {
        await this.cleanupExpiredSessions();
      },
      5 * 60 * 1000,
    ); // Run every 5 minutes
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    const sessionsToDelete: string[] = [];

    for (const session of Array.from(this.sessions.values())) {
      if (session.organizationId !== this.organizationId) continue;

      const age = now - new Date(session.metadata.createdAt).getTime();
      const idleTime =
        now - new Date(session.metadata.lastAccessedAt).getTime();

      const isExpired =
        session.metadata.expiresAt &&
        now > new Date(session.metadata.expiresAt).getTime();
      const isTooOld = age > this.memoryLimits.maxSessionAge;
      const isTooIdle =
        idleTime > this.memoryLimits.maxIdleTime &&
        session.metadata.priority !== "critical";

      if (isExpired || isTooOld || isTooIdle) {
        sessionsToDelete.push(session.id);
      }
    }

    for (const sessionId of sessionsToDelete) {
      await this.deleteSession(sessionId);
    }
  }

  // Simulated Redis operations
  private async persistSession(session: SessionData): Promise<void> {
    // In production, this would save to Redis
    console.log(`Persisting session ${session.id} to Redis`);
  }

  private async loadSession(sessionId: string): Promise<SessionData | undefined> {
    // In production, this would load from Redis
    console.log(`Loading session ${sessionId} from Redis`);
    return undefined;
  }

  private async deletePersistedSession(sessionId: string): Promise<void> {
    // In production, this would delete from Redis
    console.log(`Deleting session ${sessionId} from Redis`);
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.sessions.clear();
  }
}

// Singleton instances per organization
const sessionManagers = new Map<string, SessionManager>();

export function getSessionManager(
  organizationId: string,
  limits?: Partial<MemoryLimits>,
): SessionManager {
  if (!sessionManagers.has(organizationId)) {
    sessionManagers.set(
      organizationId,
      new SessionManager(organizationId, limits),
    );
  }
  return sessionManagers.get(organizationId)!;
}

export {
  SessionManager,
  type SessionData,
  type MemoryLimits,
  type SessionAnalytics,
};
