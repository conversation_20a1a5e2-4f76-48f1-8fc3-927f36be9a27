"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Search,
  Star,
  Clock,
  Tag,
  ChevronDown,
  Info,
  Check,
  Shield,
  Activity,
  Wifi,
  WifiOff,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../ui/card";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";
import { ScrollArea } from "../ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { getAPXClient, type APXEvent } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";

interface TemplateVariable {
  name: string;
  type: "string" | "number" | "boolean" | "array";
  description: string;
  required: boolean;
  defaultValue?: any;
}

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  version: string;
  category: string;
  tags: string[];
  rating: number;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  variables: TemplateVariable[];
  performance?: {
    avgResponseTime: number;
    successRate: number;
    costPerUse: number;
  };
}

interface PromptTemplateSelectorProps {
  onSelectTemplate?: (template: PromptTemplate) => void;
  selectedTemplateId?: string;
}

const PromptTemplateSelector: React.FC<PromptTemplateSelectorProps> = ({
  onSelectTemplate = () => {},
  selectedTemplateId = "",
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedTemplate, setSelectedTemplate] =
    useState<PromptTemplate | null>(null);

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [realtimeTemplates, setRealtimeTemplates] = useState<PromptTemplate[]>(
    [],
  );
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canViewTemplates = checkPermission("templates", "read");
  const canUseTemplates = checkPermission("templates", "use");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization && canViewTemplates) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to template events
          apixClient.subscribe(
            "templates",
            ["created", "updated", "deleted", "used"],
            handleRealtimeEvent,
          );

          // Create session for template selector
          const newSessionId = await sessionManager.createSession(
            "templates",
            "user",
            { selectedCategory, searchQuery },
            {
              tags: ["template-selector"],
              conversationId: `selector-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX for templates:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization, canViewTemplates]);

  const handleRealtimeEvent = useCallback((event: APXEvent) => {
    // Handle real-time template updates
    switch (event.type) {
      case "created":
      case "updated":
        // Update template in real-time
        setRealtimeTemplates((prev) => {
          const existing = prev.find((t) => t.id === event.data.templateId);
          if (existing) {
            return prev.map((t) =>
              t.id === event.data.templateId
                ? { ...t, ...event.data.template }
                : t,
            );
          } else {
            return [...prev, event.data.template];
          }
        });
        break;
      case "deleted":
        setRealtimeTemplates((prev) =>
          prev.filter((t) => t.id !== event.data.templateId),
        );
        break;
      case "used":
        // Update usage count in real-time
        setRealtimeTemplates((prev) =>
          prev.map((t) =>
            t.id === event.data.templateId
              ? { ...t, usageCount: t.usageCount + 1 }
              : t,
          ),
        );
        break;
    }
  }, []);

  // Organization-scoped mock data for templates
  const mockTemplates: PromptTemplate[] = [
    {
      id: "template-1",
      name: "Customer Support Agent",
      description:
        "A helpful customer support agent that can answer product questions and handle complaints.",
      content:
        "You are a customer support agent for {{company_name}}. You help customers with questions about {{product_name}} and handle their complaints professionally.",
      version: "1.2.0",
      category: "customer-service",
      tags: ["support", "customer service", "help desk"],
      rating: 4.8,
      usageCount: 1245,
      createdAt: "2023-09-15T10:30:00Z",
      updatedAt: "2023-11-20T14:45:00Z",
      variables: [
        {
          name: "company_name",
          type: "string",
          description: "The name of the company",
          required: true,
        },
        {
          name: "product_name",
          type: "string",
          description: "The name of the product",
          required: true,
        },
      ],
      performance: {
        avgResponseTime: 1.2,
        successRate: 0.95,
        costPerUse: 0.03,
      },
    },
    {
      id: "template-2",
      name: "Product Recommendation",
      description:
        "An agent that recommends products based on customer preferences and needs.",
      content:
        "You are a product recommendation specialist for {{company_name}}. Based on the customer's preferences and needs, recommend suitable products from our {{product_category}} catalog.",
      version: "1.0.5",
      category: "sales",
      tags: ["recommendations", "products", "sales"],
      rating: 4.5,
      usageCount: 876,
      createdAt: "2023-10-05T09:15:00Z",
      updatedAt: "2023-12-01T11:30:00Z",
      variables: [
        {
          name: "company_name",
          type: "string",
          description: "The name of the company",
          required: true,
        },
        {
          name: "product_category",
          type: "string",
          description: "The category of products",
          required: true,
        },
      ],
      performance: {
        avgResponseTime: 1.5,
        successRate: 0.92,
        costPerUse: 0.04,
      },
    },
    {
      id: "template-3",
      name: "Technical Support",
      description:
        "A technical support agent that can troubleshoot software and hardware issues.",
      content:
        "You are a technical support specialist for {{product_name}} version {{version_number}}. Help users troubleshoot issues with detailed step-by-step instructions.",
      version: "2.1.0",
      category: "technical",
      tags: ["technical support", "troubleshooting", "IT help"],
      rating: 4.9,
      usageCount: 2134,
      createdAt: "2023-08-20T14:00:00Z",
      updatedAt: "2023-12-10T16:20:00Z",
      variables: [
        {
          name: "product_name",
          type: "string",
          description: "The name of the product",
          required: true,
        },
        {
          name: "version_number",
          type: "string",
          description: "The version number of the product",
          required: true,
        },
      ],
      performance: {
        avgResponseTime: 1.8,
        successRate: 0.97,
        costPerUse: 0.05,
      },
    },
  ];

  // Combine mock templates with real-time updates
  const allTemplates = [...mockTemplates, ...realtimeTemplates];

  // Filter templates based on search query, category, and organization scope
  const filteredTemplates = allTemplates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesCategory =
      selectedCategory === "all" || template.category === selectedCategory;

    // Organization-scoped filtering would be applied here in production
    const matchesOrganization = true; // All templates are org-scoped

    return matchesSearch && matchesCategory && matchesOrganization;
  });

  const handleTemplateSelect = (template: PromptTemplate) => {
    // Check permissions
    if (!canUseTemplates) {
      alert("You do not have permission to use templates.");
      return;
    }

    setSelectedTemplate(template);

    // Update session with selection
    if (sessionId) {
      sessionManager.updateSession(sessionId, {
        selectedTemplate: template,
        selectionTime: new Date().toISOString(),
      });
    }

    // Broadcast template usage event
    if (isConnected && user && organization) {
      const apixClient = getAPXClient(
        organization.id,
        user.id,
        mockAuthContext.token!,
      );
      apixClient.sendEvent({
        id: `template-used-${Date.now()}`,
        type: "used",
        module: "templates",
        organizationId: organization.id,
        userId: user.id,
        timestamp: new Date().toISOString(),
        data: { templateId: template.id, template },
        version: 1,
      });
    }

    onSelectTemplate(template);
  };

  // Update session when search/category changes
  useEffect(() => {
    if (sessionId) {
      sessionManager.updateSession(sessionId, {
        selectedCategory,
        searchQuery,
        lastActivity: new Date().toISOString(),
      });
    }
  }, [selectedCategory, searchQuery, sessionId]);

  const categories = [
    { id: "all", name: "All Templates" },
    { id: "customer-service", name: "Customer Service" },
    { id: "sales", name: "Sales" },
    { id: "technical", name: "Technical" },
    { id: "marketing", name: "Marketing" },
  ];

  // Show permission error if user can't view templates
  if (!canViewTemplates) {
    return (
      <div className="bg-background border rounded-lg w-full max-w-md flex flex-col h-full p-4">
        <div className="flex items-center justify-center h-full text-center">
          <div className="space-y-2">
            <Shield className="h-8 w-8 text-muted-foreground mx-auto" />
            <p className="text-muted-foreground">
              No permission to view templates
            </p>
            <p className="text-sm text-muted-foreground">
              Contact your administrator for access
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background border rounded-lg w-full max-w-md flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold">Prompt Templates</h2>
          <div className="flex items-center gap-2">
            {/* Connection status */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  {isConnected ? (
                    <Wifi className="h-4 w-4 text-green-500" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-red-500" />
                  )}
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {isConnected
                      ? "Connected to APIX"
                      : "Disconnected from APIX"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Organization badge */}
            <Badge variant="outline" className="text-xs">
              {organization?.name}
            </Badge>
          </div>
        </div>
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={!canViewTemplates}
          />
        </div>
      </div>

      <Tabs defaultValue="browse" className="flex-1 flex flex-col">
        <div className="px-4 border-b">
          <TabsList className="w-full">
            <TabsTrigger value="browse" className="flex-1">
              Browse
            </TabsTrigger>
            <TabsTrigger value="favorites" className="flex-1">
              Favorites
            </TabsTrigger>
            <TabsTrigger value="recent" className="flex-1">
              Recent
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="browse" className="flex-1 flex flex-col p-0 m-0">
          <div className="p-2 border-b overflow-x-auto">
            <div className="flex space-x-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={
                    selectedCategory === category.id ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="whitespace-nowrap"
                >
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4 space-y-3">
              {filteredTemplates.length > 0 ? (
                filteredTemplates.map((template) => (
                  <Card
                    key={template.id}
                    className={`cursor-pointer hover:border-primary transition-colors ${selectedTemplateId === template.id ? "border-primary" : ""}`}
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <CardHeader className="p-4 pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-base">
                          {template.name}
                        </CardTitle>
                        <Badge variant="outline" className="ml-2">
                          {template.version}
                        </Badge>
                      </div>
                      <CardDescription className="line-clamp-2">
                        {template.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 pt-0 pb-2">
                      <div className="flex flex-wrap gap-1">
                        {template.tags.map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs"
                          >
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-2 text-xs text-muted-foreground flex justify-between">
                      <div className="flex items-center">
                        <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                        <span>{template.rating}</span>
                        <span className="mx-2">•</span>
                        <span>{template.usageCount} uses</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>
                          {new Date(template.updatedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </CardFooter>
                  </Card>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No templates found matching your search.
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="favorites" className="flex-1 p-4">
          <div className="text-center py-8 text-muted-foreground">
            Your favorite templates will appear here.
          </div>
        </TabsContent>

        <TabsContent value="recent" className="flex-1 p-4">
          <div className="text-center py-8 text-muted-foreground">
            Your recently used templates will appear here.
          </div>
        </TabsContent>
      </Tabs>

      {selectedTemplate && (
        <div className="p-4 border-t">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-medium">Template Details</h3>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>View detailed performance metrics</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium mb-1">Variables</h4>
              <div className="space-y-2">
                {selectedTemplate.variables.map((variable) => (
                  <div
                    key={variable.name}
                    className="flex items-center text-sm"
                  >
                    <Check className="h-3 w-3 mr-2 text-green-500" />
                    <span className="font-mono">{`{{${variable.name}}}`}</span>
                    <span className="mx-2 text-muted-foreground">-</span>
                    <span className="text-muted-foreground">
                      {variable.description}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {selectedTemplate.performance && (
              <div>
                <h4 className="text-sm font-medium mb-1">Performance</h4>
                <div className="grid grid-cols-3 gap-2">
                  <div className="bg-muted rounded p-2 text-center">
                    <div className="text-xs text-muted-foreground">
                      Response Time
                    </div>
                    <div className="font-medium">
                      {selectedTemplate.performance.avgResponseTime}s
                    </div>
                  </div>
                  <div className="bg-muted rounded p-2 text-center">
                    <div className="text-xs text-muted-foreground">
                      Success Rate
                    </div>
                    <div className="font-medium">
                      {(selectedTemplate.performance.successRate * 100).toFixed(
                        0,
                      )}
                      %
                    </div>
                  </div>
                  <div className="bg-muted rounded p-2 text-center">
                    <div className="text-xs text-muted-foreground">
                      Cost/Use
                    </div>
                    <div className="font-medium">
                      ${selectedTemplate.performance.costPerUse.toFixed(3)}
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Button
                className="w-full"
                onClick={() => onSelectTemplate(selectedTemplate)}
                disabled={!canUseTemplates}
              >
                Use This Template
              </Button>

              {/* Session info */}
              {sessionId && (
                <div className="text-xs text-muted-foreground text-center">
                  Session: {sessionId.split("_").pop()}
                </div>
              )}

              {/* Permission warning */}
              {!canUseTemplates && (
                <div className="text-xs text-red-500 text-center">
                  No permission to use templates
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromptTemplateSelector;
