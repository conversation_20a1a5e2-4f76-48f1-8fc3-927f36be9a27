"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Separator } from "../ui/separator";
import { Badge } from "../ui/badge";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Code,
  Cog,
  Copy,
  Play,
  Save,
  Settings,
  Trash,
  Shield,
  Activity,
  Database,
  Wifi,
  WifiOff,
} from "lucide-react";
import { <PERSON><PERSON>, AlertDescription } from "../ui/alert";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "../ui/tooltip";
import PromptTemplateSelector from "./PromptTemplateSelector";
import { getAPXClient, type APXEvent } from "../../lib/apix-client";
import { mockAuthContext, hasPermission } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { PromptTemplate } from "@/lib/prompt-template";

interface AgentBuilderProps {
  agentId?: string;
  initialData?: AgentData;
  onSave?: (data: AgentData) => void;
  onTest?: (data: AgentData) => void;
}

interface AgentData {
  name: string;
  description: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  templateId?: string;
  isPublic: boolean;
  version: string;
}

const AgentBuilder: React.FC<AgentBuilderProps> = ({
  agentId,
  initialData,
  onSave = () => {},
  onTest = () => {},
}) => {
  const defaultData: AgentData = {
    name: "",
    description: "",
    provider: "openai",
    model: "gpt-4o",
    temperature: 0.7,
    maxTokens: 2048,
    systemPrompt: "You are a helpful AI assistant.",
    templateId: undefined,
    isPublic: false,
    version: "1.0.0",
  };

  const [agentData, setAgentData] = useState<AgentData>(
    initialData || defaultData,
  );
  const [activeTab, setActiveTab] = useState("settings");
  const [isTesting, setIsTesting] = useState(false);
  const [testInput, setTestInput] = useState("");
  const [testResponse, setTestResponse] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | undefined>(
    agentData.templateId,
  );

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [realtimeEvents, setRealtimeEvents] = useState<APXEvent[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateAgent = checkPermission("agents", "create");
  const canEditAgent = checkPermission("agents", "update");
  const canDeleteAgent = checkPermission("agents", "delete");
  const canTestAgent = checkPermission("agents", "execute");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Quota enforcement
  const quotaUsage = organization?.usage || {
    agents: 0,
    executions: 0,
    storage: 0,
    apiCalls: 0,
  };
  const quotaLimits = organization?.quotas || {
    agents: 100,
    executions: 10000,
    storage: 1000000000,
    apiCalls: 50000,
  };
  const isQuotaExceeded = quotaUsage.agents >= quotaLimits.agents;

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to agent events
          apixClient.subscribe(
            "agents",
            ["created", "updated", "deleted", "tested"],
            handleRealtimeEvent,
          );

          // Create session for this agent builder instance
          const newSessionId = await sessionManager.createSession(
            "agents",
            "user",
            { agentId, builderState: agentData },
            {
              tags: ["agent-builder"],
              conversationId: `builder-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      // Cleanup on unmount
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization]);

  const handleRealtimeEvent = useCallback(
    (event: APXEvent) => {
      setRealtimeEvents((prev) => [event, ...prev.slice(0, 9)]); // Keep last 10 events

      // Handle specific event types
      switch (event.type) {
        case "updated":
          if (event.data.agentId === agentId) {
            // Agent was updated by another user - show notification
            console.log("Agent updated by another user:", event.data);
          }
          break;
        case "tested":
          if (event.data.agentId === agentId) {
            // Real-time test results
            setTestResponse(event.data.response);
            setIsTesting(false);
          }
          break;
      }
    },
    [agentId],
  );

  const handleInputChange = (field: keyof AgentData, value: any) => {
    setAgentData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Update session with new state
      if (sessionId) {
        sessionManager.updateSession(sessionId, { builderState: newData });
      }

      // Broadcast real-time update
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-update-${Date.now()}`,
          type: "agent_builder_change",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: { agentId, field, value, builderState: newData },
          version: 1,
        });
      }

      return newData;
    });
  };

  const handleSave = async () => {
    // Check permissions
    if (!canCreateAgent && !canEditAgent) {
      alert("You do not have permission to save agents.");
      return;
    }

    // Check quota limits
    if (!agentId && isQuotaExceeded) {
      alert(
        `Agent quota exceeded. You have used ${quotaUsage.agents}/${quotaLimits.agents} agents.`,
      );
      return;
    }

    setIsSaving(true);
    try {
      // Validate required fields
      if (!agentData.name.trim()) {
        throw new Error("Agent name is required");
      }

      // Organization-scoped save with tenant isolation
      const saveData = {
        ...agentData,
        organizationId: organization?.id,
        userId: user?.id,
        lastModified: new Date().toISOString(),
      };

      // In a real implementation, this would call an API with organization context
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Broadcast save event via APIX
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-saved-${Date.now()}`,
          type: agentId ? "updated" : "created",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || `new-${Date.now()}`,
            agentData: saveData,
          },
          version: 1,
        });
      }

      // Update session
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          builderState: saveData,
          lastSaved: new Date().toISOString(),
        });
      }

      onSave(agentData);
    } catch (error) {
      console.error("Error saving agent:", error);
      alert(
        `Error saving agent: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    if (!testInput.trim()) return;

    // Check permissions
    if (!canTestAgent) {
      alert("You do not have permission to test agents.");
      return;
    }

    // Check execution quota
    if (quotaUsage.executions >= quotaLimits.executions) {
      alert(
        `Execution quota exceeded. You have used ${quotaUsage.executions}/${quotaLimits.executions} executions.`,
      );
      return;
    }

    setIsTesting(true);
    setTestResponse("");

    try {
      // Create test session
      const testSessionId = await sessionManager.createSession(
        "agents",
        "agent",
        {
          agentConfig: agentData,
          testInput,
          startTime: new Date().toISOString(),
        },
        {
          parentSessionId: sessionId || "default",
          tags: ["test", "agent-execution"],
          conversationId: `test-${Date.now()}`,
        },
      );

      // Broadcast test start event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-test-start-${Date.now()}`,
          type: "test_started",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || "new-agent",
            testSessionId,
            input: testInput,
            config: agentData,
          },
          version: 1,
        });
      }

      // Simulate API call with organization context
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const response = `[ORGANIZATION: ${organization?.name}] This is a simulated response from the AI agent based on your input: "${testInput}".\n\nThe agent would process this using the ${agentData.provider} ${agentData.model} model with a temperature of ${agentData.temperature}.\n\nSession ID: ${testSessionId}\nUser Role: ${user?.role}\nQuota Usage: ${quotaUsage.executions + 1}/${quotaLimits.executions} executions\n\nIn a real implementation, this would connect to the actual AI provider and return a genuine response.`;

      setTestResponse(response);

      // Update test session with results
      await sessionManager.updateSession(testSessionId, {
        response,
        endTime: new Date().toISOString(),
        success: true,
      });

      // Broadcast test completion
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-test-complete-${Date.now()}`,
          type: "tested",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || "new-agent",
            testSessionId,
            response,
            success: true,
          },
          version: 1,
        });
      }
    } catch (error) {
      console.error("Error testing agent:", error);
      const errorMessage = "An error occurred while testing the agent.";
      setTestResponse(errorMessage);

      // Broadcast test error
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `agent-test-error-${Date.now()}`,
          type: "test_error",
          module: "agents",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            agentId: agentId || "new-agent",
            error: error instanceof Error ? error.message : "Unknown error",
          },
          version: 1,
        });
      }
    } finally {
      setIsTesting(false);
    }
  };

    const handleTemplateSelect = (template: any) => {
    setSelectedTemplate(template.id);
    // In a real implementation, this would fetch the template data and update the system prompt
    handleInputChange("templateId", template.id);
    handleInputChange(
      "systemPrompt",
      template.systemPrompt || template.content || "This is a system prompt from the selected template. In a real implementation, this would be populated with the actual template content.",
    );
  };

  return (
    <div className="bg-background w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">
              {agentId ? "Edit Agent" : "Create New Agent"}
            </h1>
            <div className="flex items-center gap-2">
              {/* Real-time connection status */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    {isConnected ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* User role badge */}
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>

              {/* Organization badge */}
              <Badge variant="secondary">{organization?.name}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-4 mt-1">
            <p className="text-muted-foreground">
              {agentId
                ? `Editing agent ${agentId}`
                : "Configure your new AI agent"}
            </p>

            {/* Quota usage indicators */}
            <div className="flex items-center gap-3 text-sm">
              <div className="flex items-center gap-1">
                <Database className="h-3 w-3" />
                <span
                  className={
                    quotaUsage.agents >= quotaLimits.agents * 0.9
                      ? "text-red-500"
                      : "text-muted-foreground"
                  }
                >
                  {quotaUsage.agents}/{quotaLimits.agents} agents
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Activity className="h-3 w-3" />
                <span
                  className={
                    quotaUsage.executions >= quotaLimits.executions * 0.9
                      ? "text-red-500"
                      : "text-muted-foreground"
                  }
                >
                  {quotaUsage.executions}/{quotaLimits.executions} executions
                </span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => {}}
            disabled={!canCreateAgent}
          >
            <Copy className="mr-2 h-4 w-4" /> Duplicate
          </Button>
          <Button
            variant="destructive"
            onClick={() => {}}
            disabled={!canDeleteAgent}
          >
            <Trash className="mr-2 h-4 w-4" /> Delete
          </Button>
          <Button
            onClick={handleSave}
            disabled={
              isSaving ||
              (!canCreateAgent && !canEditAgent) ||
              (!agentId && isQuotaExceeded)
            }
          >
            {isSaving ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" /> Save Agent
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Real-time events panel */}
      {realtimeEvents.length > 0 && (
        <div className="px-4 py-2 bg-muted/50 border-b">
          <div className="flex items-center gap-2 text-sm">
            <Activity className="h-4 w-4 text-blue-500" />
            <span className="font-medium">Recent Events:</span>
            <div className="flex gap-2 overflow-x-auto">
              {realtimeEvents.slice(0, 3).map((event, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs whitespace-nowrap"
                >
                  {event.type} •{" "}
                  {new Date(event.timestamp).toLocaleTimeString()}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Quota warning */}
      {isQuotaExceeded && (
        <Alert className="mx-4 mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Agent quota exceeded. You have reached the limit of{" "}
            {quotaLimits.agents} agents for your organization. Please contact
            your administrator to increase your quota.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-1 overflow-hidden">
        <div className="w-2/3 p-4 overflow-y-auto">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-4">
              <TabsTrigger value="settings">
                <Settings className="mr-2 h-4 w-4" /> Settings
              </TabsTrigger>
              <TabsTrigger value="prompt">
                <Code className="mr-2 h-4 w-4" /> Prompt Template
              </TabsTrigger>
              <TabsTrigger value="advanced">
                <Cog className="mr-2 h-4 w-4" /> Advanced
              </TabsTrigger>
            </TabsList>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Configure the basic settings for your agent.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Agent Name</Label>
                      <Input
                        id="name"
                        value={agentData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        placeholder="My AI Agent"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="version">Version</Label>
                      <Input
                        id="version"
                        value={agentData.version}
                        onChange={(e) =>
                          handleInputChange("version", e.target.value)
                        }
                        placeholder="1.0.0"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={agentData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      placeholder="Describe what this agent does..."
                      rows={3}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isPublic"
                      checked={agentData.isPublic}
                      onCheckedChange={(checked) =>
                        handleInputChange("isPublic", checked)
                      }
                    />
                    <Label htmlFor="isPublic">Make this agent public</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Provider Configuration</CardTitle>
                  <CardDescription>
                    Select the AI provider and model for your agent.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="provider">Provider</Label>
                      <Select
                        value={agentData.provider}
                        onValueChange={(value) =>
                          handleInputChange("provider", value)
                        }
                      >
                        <SelectTrigger id="provider">
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="anthropic">Anthropic</SelectItem>
                          <SelectItem value="google">Google AI</SelectItem>
                          <SelectItem value="mistral">Mistral AI</SelectItem>
                          <SelectItem value="groq">Groq</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="model">Model</Label>
                      <Select
                        value={agentData.model}
                        onValueChange={(value) =>
                          handleInputChange("model", value)
                        }
                      >
                        <SelectTrigger id="model">
                          <SelectValue placeholder="Select model" />
                        </SelectTrigger>
                        <SelectContent>
                          {agentData.provider === "openai" && (
                            <>
                              <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                              <SelectItem value="gpt-4-turbo">
                                GPT-4 Turbo
                              </SelectItem>
                              <SelectItem value="gpt-3.5-turbo">
                                GPT-3.5 Turbo
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "anthropic" && (
                            <>
                              <SelectItem value="claude-3-opus">
                                Claude 3 Opus
                              </SelectItem>
                              <SelectItem value="claude-3-sonnet">
                                Claude 3 Sonnet
                              </SelectItem>
                              <SelectItem value="claude-3-haiku">
                                Claude 3 Haiku
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "google" && (
                            <>
                              <SelectItem value="gemini-pro">
                                Gemini Pro
                              </SelectItem>
                              <SelectItem value="gemini-ultra">
                                Gemini Ultra
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "mistral" && (
                            <>
                              <SelectItem value="mistral-large">
                                Mistral Large
                              </SelectItem>
                              <SelectItem value="mistral-medium">
                                Mistral Medium
                              </SelectItem>
                              <SelectItem value="mistral-small">
                                Mistral Small
                              </SelectItem>
                            </>
                          )}
                          {agentData.provider === "groq" && (
                            <>
                              <SelectItem value="llama3-70b">
                                LLaMA 3 70B
                              </SelectItem>
                              <SelectItem value="llama3-8b">
                                LLaMA 3 8B
                              </SelectItem>
                              <SelectItem value="mixtral-8x7b">
                                Mixtral 8x7B
                              </SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="prompt" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Prompt Template</CardTitle>
                  <CardDescription>
                    Select a prompt template or create a custom system prompt
                    for your agent.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Label>Template Selection</Label>
                      <Badge variant={selectedTemplate ? "default" : "outline"}>
                        {selectedTemplate ? "Template Selected" : "No Template"}
                      </Badge>
                    </div>
                    <PromptTemplateSelector
                      selectedTemplateId={selectedTemplate}
                      onSelectTemplate={handleTemplateSelect}
                    />

                    <Separator className="my-4" />

                    <div className="space-y-2">
                      <Label htmlFor="systemPrompt">System Prompt</Label>
                      <Textarea
                        id="systemPrompt"
                        value={agentData.systemPrompt}
                        onChange={(e) =>
                          handleInputChange("systemPrompt", e.target.value)
                        }
                        placeholder="Enter system prompt..."
                        rows={10}
                        className="font-mono"
                      />
                      <p className="text-sm text-muted-foreground">
                        The system prompt defines the behavior and capabilities
                        of your agent.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Settings</CardTitle>
                  <CardDescription>
                    Configure advanced parameters for your agent.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="temperature">
                          Temperature: {agentData.temperature}
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>
                                Controls randomness: 0 is deterministic, 1 is
                                creative
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Input
                        id="temperature"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={agentData.temperature}
                        onChange={(e) =>
                          handleInputChange(
                            "temperature",
                            parseFloat(e.target.value),
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="maxTokens">
                          Max Tokens: {agentData.maxTokens}
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <AlertCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Maximum number of tokens to generate</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <Input
                        id="maxTokens"
                        type="range"
                        min="256"
                        max="4096"
                        step="256"
                        value={agentData.maxTokens}
                        onChange={(e) =>
                          handleInputChange(
                            "maxTokens",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="w-1/3 border-l p-4 overflow-y-auto">
          <Card className="h-full flex flex-col">
            <CardHeader>
              <CardTitle>Test Your Agent</CardTitle>
              <CardDescription>
                Try out your agent with a test prompt to see how it responds.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1 overflow-y-auto">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="testInput">Test Prompt</Label>
                  <Textarea
                    id="testInput"
                    value={testInput}
                    onChange={(e) => setTestInput(e.target.value)}
                    placeholder="Enter a test prompt for your agent..."
                    rows={4}
                  />
                </div>

                {testResponse && (
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label className="flex-1">Response</Label>
                      <Badge variant="outline" className="ml-2">
                        {agentData.provider} / {agentData.model}
                      </Badge>
                    </div>
                    <div className="bg-muted p-4 rounded-md whitespace-pre-wrap font-mono text-sm overflow-y-auto max-h-[400px]">
                      {testResponse}
                    </div>
                  </div>
                )}

                {!testResponse && !isTesting && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Enter a test prompt and click "Test Agent" to see a
                      response.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="space-y-2">
                <Button
                  onClick={handleTest}
                  disabled={
                    isTesting ||
                    !testInput.trim() ||
                    !canTestAgent ||
                    quotaUsage.executions >= quotaLimits.executions
                  }
                  className="w-full"
                >
                  {isTesting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span> Testing...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" /> Test Agent
                    </>
                  )}
                </Button>

                {/* Session info */}
                {sessionId && (
                  <div className="text-xs text-muted-foreground text-center">
                    Session: {sessionId.split("_").pop()}
                  </div>
                )}

                {/* Permission/quota warnings */}
                {!canTestAgent && (
                  <div className="text-xs text-red-500 text-center">
                    No permission to test agents
                  </div>
                )}
                {quotaUsage.executions >= quotaLimits.executions && (
                  <div className="text-xs text-red-500 text-center">
                    Execution quota exceeded
                  </div>
                )}
              </div>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AgentBuilder;
