"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "../ui/tabs";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Zap,
  DollarSign,
  Clock,
  Activity,
  Download,
  RefreshCw,
  Filter,
  Calendar,
  Wifi,
  WifiOff,
  Shield,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>rigger,
} from "../ui/tooltip";
import { getAPXClient } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { createAnalyticsUpdatedEvent } from "../../lib/apix-events";

interface MetricData {
  name: string;
  value: number;
  change: number;
  trend: "up" | "down" | "stable";
  format: "number" | "currency" | "percentage" | "duration";
}

interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    color: string;
  }>;
}

interface AnalyticsFilter {
  timeRange: "1h" | "24h" | "7d" | "30d" | "90d" | "custom";
  modules: string[];
  organizations: string[];
  customStart?: string;
  customEnd?: string;
}

const AnalyticsDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [filter, setFilter] = useState<AnalyticsFilter>({
    timeRange: "24h",
    modules: ["agents", "tools", "hybrids", "widgets"],
    organizations: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [realtimeMetrics, setRealtimeMetrics] = useState<Record<string, any>>(
    {},
  );

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canViewAnalytics = checkPermission("analytics", "read");
  const canExportReports = checkPermission("analytics", "export");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Mock analytics data
  const overviewMetrics: MetricData[] = [
    {
      name: "Total Executions",
      value: 12847,
      change: 12.5,
      trend: "up",
      format: "number",
    },
    {
      name: "Active Users",
      value: 1284,
      change: 8.2,
      trend: "up",
      format: "number",
    },
    {
      name: "Total Cost",
      value: 2847.32,
      change: -5.1,
      trend: "down",
      format: "currency",
    },
    {
      name: "Avg Response Time",
      value: 1.2,
      change: -15.3,
      trend: "down",
      format: "duration",
    },
    {
      name: "Success Rate",
      value: 94.7,
      change: 2.1,
      trend: "up",
      format: "percentage",
    },
    {
      name: "Widget Interactions",
      value: 5632,
      change: 23.8,
      trend: "up",
      format: "number",
    },
  ];

  const agentMetrics: MetricData[] = [
    {
      name: "Agent Executions",
      value: 8234,
      change: 15.2,
      trend: "up",
      format: "number",
    },
    {
      name: "Avg Agent Cost",
      value: 0.12,
      change: -8.5,
      trend: "down",
      format: "currency",
    },
    {
      name: "Agent Success Rate",
      value: 96.3,
      change: 1.8,
      trend: "up",
      format: "percentage",
    },
    {
      name: "Popular Templates",
      value: 42,
      change: 5.7,
      trend: "up",
      format: "number",
    },
  ];

  const toolMetrics: MetricData[] = [
    {
      name: "Tool Executions",
      value: 3421,
      change: 9.8,
      trend: "up",
      format: "number",
    },
    {
      name: "Tool Chains",
      value: 156,
      change: 18.3,
      trend: "up",
      format: "number",
    },
    {
      name: "API Integrations",
      value: 89,
      change: 12.1,
      trend: "up",
      format: "number",
    },
    {
      name: "Tool Reliability",
      value: 98.1,
      change: 0.5,
      trend: "stable",
      format: "percentage",
    },
  ];

  const hybridMetrics: MetricData[] = [
    {
      name: "Workflow Executions",
      value: 1192,
      change: 22.4,
      trend: "up",
      format: "number",
    },
    {
      name: "Avg Workflow Steps",
      value: 4.2,
      change: 8.7,
      trend: "up",
      format: "number",
    },
    {
      name: "Provider Switches",
      value: 234,
      change: -12.3,
      trend: "down",
      format: "number",
    },
    {
      name: "Hybrid Success Rate",
      value: 91.8,
      change: 3.2,
      trend: "up",
      format: "percentage",
    },
  ];

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization && canViewAnalytics) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to analytics events
          apixClient.subscribe(
            "analytics",
            ["updated", "dashboard_viewed", "report_generated"],
            handleRealtimeEvent,
          );

          // Create session for analytics dashboard
          const newSessionId = await sessionManager.createSession(
            "analytics",
            "user",
            { dashboardView: activeTab, filters: filter },
            {
              tags: ["analytics-dashboard"],
              conversationId: `dashboard-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);

          // Broadcast dashboard view event
          apixClient.sendEvent({
            id: `dashboard-viewed-${Date.now()}`,
            type: "dashboard_viewed",
            module: "analytics",
            organizationId: organization.id,
            userId: user.id,
            timestamp: new Date().toISOString(),
            data: {
              dashboardId: "main-dashboard",
              viewType: activeTab,
              filters: filter,
              timeRange: {
                start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                end: new Date().toISOString(),
              },
            },
            version: 1,
          });
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization, canViewAnalytics]);

  const handleRealtimeEvent = useCallback((event: any) => {
    switch (event.type) {
      case "analytics_updated":
        setRealtimeMetrics((prev) => ({
          ...prev,
          [event.data.metricType]: event.data.value,
        }));
        setLastUpdated(new Date());
        break;
      case "dashboard_viewed":
        console.log("Dashboard viewed:", event.data);
        break;
      case "report_generated":
        console.log("Report generated:", event.data);
        break;
    }
  }, []);

  const formatMetricValue = (value: number, format: string) => {
    switch (format) {
      case "currency":
        return `$${value.toLocaleString(undefined, { minimumFractionDigits: 2 })}`;
      case "percentage":
        return `${value.toFixed(1)}%`;
      case "duration":
        return `${value.toFixed(1)}s`;
      default:
        return value.toLocaleString();
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      // Simulate data refresh
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setLastUpdated(new Date());

      // Broadcast analytics update event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );

        const analyticsEvent = createAnalyticsUpdatedEvent(
          organization.id,
          user.id,
          {
            metricType: "dashboard_refresh",
            value: Date.now(),
            timestamp: new Date().toISOString(),
            dimensions: { tab: activeTab, filters: filter },
            aggregationType: "count",
          },
        );

        apixClient.sendEvent(analyticsEvent);
      }
    } catch (error) {
      console.error("Error refreshing analytics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportReport = async () => {
    if (!canExportReports) {
      alert("You do not have permission to export reports.");
      return;
    }

    try {
      // Broadcast report generation event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );

        apixClient.sendEvent({
          id: `report-generated-${Date.now()}`,
          type: "report_generated",
          module: "analytics",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            reportId: `report-${Date.now()}`,
            reportType: "performance",
            format: "pdf",
            filters: filter,
            generatedBy: user.id,
            downloadUrl: `https://reports.synapseai.com/download/report-${Date.now()}.pdf`,
          },
          version: 1,
        });
      }

      // Simulate report generation
      alert(
        "Report generation started. You will receive a download link via email.",
      );
    } catch (error) {
      console.error("Error generating report:", error);
    }
  };

  const renderMetricCards = (metrics: MetricData[]) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {metrics.map((metric) => (
        <Card key={metric.name}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">{metric.name}</p>
                <p className="text-2xl font-bold">
                  {formatMetricValue(metric.value, metric.format)}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  {getTrendIcon(metric.trend)}
                  <span
                    className={`text-sm ${
                      metric.trend === "up"
                        ? "text-green-500"
                        : metric.trend === "down"
                          ? "text-red-500"
                          : "text-gray-500"
                    }`}
                  >
                    {metric.change > 0 ? "+" : ""}
                    {metric.change.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div className="text-right">
                {metric.name.includes("Executions") && (
                  <Zap className="h-8 w-8 text-blue-500" />
                )}
                {metric.name.includes("Users") && (
                  <Users className="h-8 w-8 text-green-500" />
                )}
                {metric.name.includes("Cost") && (
                  <DollarSign className="h-8 w-8 text-yellow-500" />
                )}
                {metric.name.includes("Time") && (
                  <Clock className="h-8 w-8 text-purple-500" />
                )}
                {metric.name.includes("Rate") && (
                  <BarChart3 className="h-8 w-8 text-indigo-500" />
                )}
                {metric.name.includes("Widget") && (
                  <Activity className="h-8 w-8 text-pink-500" />
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Show permission error if user can't view analytics
  if (!canViewAnalytics) {
    return (
      <div className="bg-background min-h-screen p-6 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center space-y-4">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto" />
            <div>
              <h2 className="text-xl font-semibold">Access Denied</h2>
              <p className="text-muted-foreground mt-2">
                You do not have permission to view analytics.
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Contact your administrator for access.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 mt-2">
              Real-time insights across all platform modules
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? (
                <Wifi className="h-3 w-3 mr-1" />
              ) : (
                <WifiOff className="h-3 w-3 mr-1" />
              )}
              {isConnected ? "Live" : "Offline"}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Shield className="h-3 w-3" />
              {user?.role}
            </Badge>
            <Badge variant="secondary">{organization?.name}</Badge>
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <Button onClick={handleExportReport} disabled={!canExportReports}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Label>Time Range:</Label>
                <Select
                  value={filter.timeRange}
                  onValueChange={(value: any) =>
                    setFilter({ ...filter, timeRange: value })
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1h">Last Hour</SelectItem>
                    <SelectItem value="24h">Last 24h</SelectItem>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="agents">Agents</TabsTrigger>
            <TabsTrigger value="tools">Tools</TabsTrigger>
            <TabsTrigger value="hybrids">Hybrids</TabsTrigger>
            <TabsTrigger value="widgets">Widgets</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {renderMetricCards(overviewMetrics)}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Execution Trends</CardTitle>
                  <CardDescription>
                    Real-time execution patterns across all modules
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-muted-foreground">
                    Chart visualization would be rendered here
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Cost Analysis</CardTitle>
                  <CardDescription>
                    Provider costs and optimization opportunities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>OpenAI GPT-4</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          $1,234.56
                        </span>
                        <Badge variant="outline">43%</Badge>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Claude 3 Sonnet</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          $892.34
                        </span>
                        <Badge variant="outline">31%</Badge>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Gemini Pro</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          $720.42
                        </span>
                        <Badge variant="outline">26%</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="agents" className="space-y-6">
            {renderMetricCards(agentMetrics)}

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Agents</CardTitle>
                <CardDescription>
                  Agents with highest success rates and usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      name: "Customer Support Agent",
                      executions: 2847,
                      successRate: 96.3,
                      cost: 0.12,
                    },
                    {
                      name: "Data Analysis Assistant",
                      executions: 1923,
                      successRate: 94.8,
                      cost: 0.15,
                    },
                    {
                      name: "Content Generator",
                      executions: 1456,
                      successRate: 92.1,
                      cost: 0.08,
                    },
                  ].map((agent) => (
                    <div
                      key={agent.name}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div>
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {agent.executions.toLocaleString()} executions
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium">
                            {agent.successRate}%
                          </div>
                          <div className="text-muted-foreground">Success</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">${agent.cost}</div>
                          <div className="text-muted-foreground">Avg Cost</div>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tools" className="space-y-6">
            {renderMetricCards(toolMetrics)}

            <Card>
              <CardHeader>
                <CardTitle>Tool Performance</CardTitle>
                <CardDescription>
                  Most used tools and their reliability metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      name: "SendGrid Email",
                      executions: 1245,
                      reliability: 99.2,
                      avgTime: 0.5,
                    },
                    {
                      name: "Stripe Payments",
                      executions: 892,
                      reliability: 98.7,
                      avgTime: 1.2,
                    },
                    {
                      name: "PostgreSQL Query",
                      executions: 2156,
                      reliability: 99.8,
                      avgTime: 0.3,
                    },
                  ].map((tool) => (
                    <div
                      key={tool.name}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div>
                        <div className="font-medium">{tool.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {tool.executions.toLocaleString()} executions
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium">{tool.reliability}%</div>
                          <div className="text-muted-foreground">
                            Reliability
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{tool.avgTime}s</div>
                          <div className="text-muted-foreground">Avg Time</div>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="hybrids" className="space-y-6">
            {renderMetricCards(hybridMetrics)}

            <Card>
              <CardHeader>
                <CardTitle>Workflow Efficiency</CardTitle>
                <CardDescription>
                  Hybrid workflow performance and optimization insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      name: "Content Generation Pipeline",
                      steps: 4,
                      duration: 12.3,
                      success: 94.2,
                    },
                    {
                      name: "Code Review Assistant",
                      steps: 6,
                      duration: 18.7,
                      success: 97.8,
                    },
                    {
                      name: "Data Processing Workflow",
                      steps: 8,
                      duration: 45.2,
                      success: 89.1,
                    },
                  ].map((workflow) => (
                    <div
                      key={workflow.name}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div>
                        <div className="font-medium">{workflow.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {workflow.steps} steps
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium">
                            {workflow.duration}s
                          </div>
                          <div className="text-muted-foreground">Duration</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{workflow.success}%</div>
                          <div className="text-muted-foreground">Success</div>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="widgets" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Total Widgets
                      </p>
                      <p className="text-2xl font-bold">156</p>
                      <div className="flex items-center gap-1 mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-500">+18.3%</span>
                      </div>
                    </div>
                    <Activity className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Widget Interactions
                      </p>
                      <p className="text-2xl font-bold">5,632</p>
                      <div className="flex items-center gap-1 mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-500">+23.8%</span>
                      </div>
                    </div>
                    <Users className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Conversion Rate
                      </p>
                      <p className="text-2xl font-bold">12.4%</p>
                      <div className="flex items-center gap-1 mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-500">+2.1%</span>
                      </div>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Avg Session
                      </p>
                      <p className="text-2xl font-bold">4.2m</p>
                      <div className="flex items-center gap-1 mt-1">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="text-sm text-green-500">+8.7%</span>
                      </div>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Widget Performance</CardTitle>
                <CardDescription>
                  Top performing widgets by engagement and conversion
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      name: "Customer Support Chat",
                      interactions: 2847,
                      conversion: 15.2,
                      avgSession: 5.3,
                    },
                    {
                      name: "Product Recommendation",
                      interactions: 1923,
                      conversion: 18.7,
                      avgSession: 3.8,
                    },
                    {
                      name: "FAQ Assistant",
                      interactions: 1456,
                      conversion: 8.9,
                      avgSession: 2.1,
                    },
                  ].map((widget) => (
                    <div
                      key={widget.name}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div>
                        <div className="font-medium">{widget.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {widget.interactions.toLocaleString()} interactions
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium">
                            {widget.conversion}%
                          </div>
                          <div className="text-muted-foreground">
                            Conversion
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">
                            {widget.avgSession}m
                          </div>
                          <div className="text-muted-foreground">
                            Avg Session
                          </div>
                        </div>
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
