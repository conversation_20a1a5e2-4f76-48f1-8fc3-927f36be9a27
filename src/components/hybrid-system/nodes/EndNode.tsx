"use client";

import React, { memo } from 'react';
import { Handle, Position, NodeProps } from '@/lib/mock-reactflow';
import { Square } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface EndNodeData {
  name: string;
}

const EndNode = ({ data }: NodeProps<EndNodeData>) => {
  return (
    <Card className="w-40 shadow-md bg-red-50 border-2 border-red-200">
      <CardContent className="p-3 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Square className="h-4 w-4 text-red-600" />
          <span className="text-sm font-medium">{data.name || 'End'}</span>
        </div>
      </CardContent>
      
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(EndNode);
