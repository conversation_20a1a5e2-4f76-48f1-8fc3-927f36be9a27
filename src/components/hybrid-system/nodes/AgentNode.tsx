"use client";

import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from '@/lib/mock-reactflow';
import { <PERSON><PERSON>, Setting<PERSON> } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface AgentNodeData {
  name: string;
  description?: string;
  status?: 'idle' | 'running' | 'completed' | 'failed' | 'paused';
  config?: {
    agentId?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    userPrompt?: string;
    enabled?: boolean;
  };
  onConfigure?: (nodeId: string) => void;
}

const AgentNode = ({ id, data, selected }: NodeProps<AgentNodeData>) => {
  const statusColors = {
    idle: 'bg-gray-100',
    running: 'bg-blue-100 animate-pulse',
    completed: 'bg-green-100',
    failed: 'bg-red-100',
    paused: 'bg-yellow-100',
  };

  const statusColor = data.status ? statusColors[data.status] : statusColors.idle;

  return (
    <Card className={`w-64 shadow-md transition-all ${selected ? 'ring-2 ring-blue-500' : ''} ${statusColor}`}>
      <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-blue-600" />
          <CardTitle className="text-sm font-medium">{data.name}</CardTitle>
        </div>
        {data.status && (
          <Badge variant={data.status === 'failed' ? 'destructive' : 'outline'} className="text-xs">
            {data.status}
          </Badge>
        )}
      </CardHeader>
      <CardContent className="p-3 pt-2">
        {data.description && (
          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">{data.description}</p>
        )}
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground">
            {data.config?.model || 'No model selected'}
          </span>
          {data.onConfigure && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-6 w-6 p-0" 
              onClick={() => data.onConfigure?.(id)}
            >
              <Settings className="h-3.5 w-3.5" />
            </Button>
          )}
        </div>
      </CardContent>
      
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(AgentNode);
