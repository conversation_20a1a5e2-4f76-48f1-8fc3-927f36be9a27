"use client";

import React, { memo } from 'react';
import { Handle, Position, NodeProps } from '@/lib/mock-reactflow';
import { Circle, Play } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface StartNodeData {
  name: string;
}

const StartNode = ({ data }: NodeProps<StartNodeData>) => {
  return (
    <Card className="w-40 shadow-md bg-gray-100 border-2 border-gray-300">
      <CardContent className="p-3 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Circle className="h-4 w-4 text-gray-600 fill-gray-600" />
          <span className="text-sm font-medium">{data.name || 'Start'}</span>
        </div>
      </CardContent>
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-gray-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(StartNode);
