"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "../ui/card";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Switch } from "../ui/switch";
import { Separator } from "../ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "../ui/tabs";
import {
  Play,
  Save,
  Plus,
  Trash,
  Settings,
  GitBranch,
  Zap,
  Bot,
  Wrench,
  ArrowRight,
  ArrowDown,
  Diamond,
  Circle,
  Square,
  Wifi,
  WifiOff,
  Shield,
  Copy,
  Download,
  Upload,
  MoreVertical,
  Eye,
  Edit,
  History,
  Share,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Code,
  FileText,
  Layers,
  Network,
  Cpu,
  BarChart3,
  Users,
  Calendar,
  Tag,
  Filter,
  Search,
  RefreshCw,
  Maximize2,
  Minimize2,
  ZoomIn,
  ZoomOut,
  Move,
  RotateCcw,
  Undo2,
  Redo2,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { getAPXClient } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import {
  createHybridStepCompletedEvent,
  createToolExecutedEvent,
  createProviderSelectedEvent,
  createAnalyticsUpdatedEvent,
} from "../../lib/apix-events";

interface WorkflowNode {
  id: string;
  type:
    | "agent"
    | "tool"
    | "condition"
    | "start"
    | "end"
    | "api"
    | "webhook"
    | "database"
    | "transform";
  name: string;
  description?: string;
  position: { x: number; y: number };
  config: {
    // Agent configuration
    agentId?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    userPrompt?: string;

    // Tool configuration
    toolId?: string;
    toolName?: string;
    parameters?: Record<string, any>;
    timeout?: number;
    retries?: number;

    // Condition configuration
    conditionType?: "if" | "switch" | "loop" | "parallel";
    expression?: string;
    branches?: Array<{ condition: string; target: string }>;

    // API configuration
    method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    url?: string;
    headers?: Record<string, string>;
    body?: any;

    // Database configuration
    operation?: "select" | "insert" | "update" | "delete";
    table?: string;
    query?: string;

    // Transform configuration
    transformType?: "map" | "filter" | "reduce" | "sort";
    script?: string;

    // Common configuration
    enabled?: boolean;
    priority?: number;
    tags?: string[];
    metadata?: Record<string, any>;
  };
  connections: Array<{
    targetId: string;
    condition?: string;
    label?: string;
  }>;
  status?: "idle" | "running" | "completed" | "failed" | "paused";
  lastExecuted?: string;
  executionCount?: number;
  avgExecutionTime?: number;
  errorCount?: number;
  lastError?: string;
}

interface WorkflowData {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  nodes: WorkflowNode[];
  variables: Record<string, any>;
  settings: {
    timeout: number;
    retries: number;
    parallelism: number;
    errorHandling: "stop" | "continue" | "retry";
    logging: boolean;
    monitoring: boolean;
  };
  version: string;
  isActive: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  lastExecutedAt?: string;
  executionCount: number;
  successRate: number;
  avgExecutionTime: number;
  totalCost: number;
  permissions: {
    canEdit: boolean;
    canExecute: boolean;
    canShare: boolean;
    canDelete: boolean;
  };
}

interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: "running" | "completed" | "failed" | "cancelled";
  startedAt: string;
  completedAt?: string;
  duration?: number;
  input: any;
  output?: any;
  error?: string;
  steps: Array<{
    nodeId: string;
    status: "pending" | "running" | "completed" | "failed" | "skipped";
    startedAt?: string;
    completedAt?: string;
    duration?: number;
    input?: any;
    output?: any;
    error?: string;
  }>;
  cost: number;
  triggeredBy: string;
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  thumbnail?: string;
  nodes: WorkflowNode[];
  variables: Record<string, any>;
  settings: WorkflowData["settings"];
  isPublic: boolean;
  usageCount: number;
  rating: number;
  createdBy: string;
}

interface WorkflowBuilderProps {
  workflowId?: string;
  initialData?: WorkflowData;
  mode?: "create" | "edit" | "view" | "debug";
  onSave?: (data: WorkflowData) => Promise<void>;
  onExecute?: (data: WorkflowData, input?: any) => Promise<WorkflowExecution>;
  onDelete?: (workflowId: string) => Promise<void>;
  onShare?: (workflowId: string, permissions: any) => Promise<void>;
  onExport?: (
    workflowId: string,
    format: "json" | "yaml" | "code",
  ) => Promise<string>;
  onImport?: (data: string, format: "json" | "yaml") => Promise<WorkflowData>;
}

const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  workflowId,
  initialData,
  mode = "create",
  onSave,
  onExecute,
  onDelete,
  onShare,
  onExport,
  onImport,
}) => {
  const defaultData: WorkflowData = {
    id: workflowId || `workflow-${Date.now()}`,
    name: "New Workflow",
    description: "",
    category: "general",
    tags: [],
    nodes: [
      {
        id: "start",
        type: "start",
        name: "Start",
        position: { x: 100, y: 100 },
        config: { enabled: true },
        connections: [],
        status: "idle",
        executionCount: 0,
        errorCount: 0,
      },
      {
        id: "end",
        type: "end",
        name: "End",
        position: { x: 500, y: 300 },
        config: { enabled: true },
        connections: [],
        status: "idle",
        executionCount: 0,
        errorCount: 0,
      },
    ],
    variables: {},
    settings: {
      timeout: 300000, // 5 minutes
      retries: 3,
      parallelism: 1,
      errorHandling: "stop",
      logging: true,
      monitoring: true,
    },
    version: "1.0.0",
    isActive: false,
    isPublic: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: mockAuthContext.user?.id || "unknown",
    executionCount: 0,
    successRate: 0,
    avgExecutionTime: 0,
    totalCost: 0,
    permissions: {
      canEdit: true,
      canExecute: true,
      canShare: true,
      canDelete: true,
    },
  };

  const [workflowData, setWorkflowData] = useState<WorkflowData>(
    initialData || defaultData,
  );
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLog, setExecutionLog] = useState<string[]>([]);
  const [currentExecution, setCurrentExecution] =
    useState<WorkflowExecution | null>(null);
  const [executionHistory, setExecutionHistory] = useState<WorkflowExecution[]>(
    [],
  );
  const [availableTemplates, setAvailableTemplates] = useState<
    WorkflowTemplate[]
  >([]);
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);
  const [availableTools, setAvailableTools] = useState<any[]>([]);
  const [draggedNodeType, setDraggedNodeType] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [canvasZoom, setCanvasZoom] = useState(1);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [showGrid, setShowGrid] = useState(true);
  const [showMinimap, setShowMinimap] = useState(true);
  const [autoSave, setAutoSave] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showNodeConfig, setShowNodeConfig] = useState(false);
  const [showWorkflowSettings, setShowWorkflowSettings] = useState(false);
  const [showExecutionHistory, setShowExecutionHistory] = useState(false);
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState("all");
  const [sortBy, setSortBy] = useState("name");

  const canvasRef = useRef<HTMLDivElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<
    "connecting" | "connected" | "disconnected" | "error"
  >("disconnected");
  const [realtimeEvents, setRealtimeEvents] = useState<any[]>([]);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateWorkflow = checkPermission("hybrids", "create");
  const canEditWorkflow = checkPermission("hybrids", "update");
  const canExecuteWorkflow = checkPermission("hybrids", "execute");
  const canDeleteWorkflow = checkPermission("hybrids", "delete");
  const canShareWorkflow = checkPermission("hybrids", "share");
  const canViewAnalytics = checkPermission("analytics", "read");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // API Base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "/api";

  // WebSocket connection for real-time updates
  const wsRef = useRef<WebSocket | null>(null);

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        setConnectionStatus("connecting");
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);
          setConnectionStatus("connected");

          // Subscribe to hybrid events
          apixClient.subscribe(
            "hybrids",
            [
              "started",
              "step_completed",
              "finished",
              "execution_failed",
              "node_updated",
              "workflow_saved",
            ],
            handleRealtimeEvent,
          );

          // Subscribe to analytics events
          if (canViewAnalytics) {
            apixClient.subscribe(
              "analytics",
              ["metrics_updated", "performance_alert"],
              handleAnalyticsEvent,
            );
          }

          // Create session for workflow builder
          const newSessionId = await sessionManager.createSession(
            "hybrids",
            "user",
            { workflowId, builderState: workflowData, mode },
            {
              tags: ["workflow-builder", mode],
              conversationId: `builder-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
        setConnectionStatus("error");
        setError("Failed to connect to real-time services");
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [user, organization, mode]);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      try {
        // Load workflow data if editing
        if (workflowId && mode !== "create") {
          const workflow = await fetchWorkflow(workflowId);
          if (workflow) {
            setWorkflowData(workflow);
          }
        }

        // Load available resources
        const [agents, tools, templates] = await Promise.all([
          fetchAvailableAgents(),
          fetchAvailableTools(),
          fetchWorkflowTemplates(),
        ]);

        setAvailableAgents(agents);
        setAvailableTools(tools);
        setAvailableTemplates(templates);

        // Load execution history if viewing/editing
        if (workflowId) {
          const history = await fetchExecutionHistory(workflowId);
          setExecutionHistory(history);
        }
      } catch (error) {
        console.error("Failed to load initial data:", error);
        setError("Failed to load workflow data");
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, [workflowId, mode]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && unsavedChanges && mode !== "view") {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      autoSaveTimeoutRef.current = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [workflowData, autoSave, unsavedChanges, mode]);

  // Validation
  useEffect(() => {
    const errors = validateWorkflow(workflowData);
    setValidationErrors(errors);
  }, [workflowData]);

  // Mark unsaved changes
  useEffect(() => {
    if (
      initialData &&
      JSON.stringify(workflowData) !== JSON.stringify(initialData)
    ) {
      setUnsavedChanges(true);
    }
  }, [workflowData, initialData]);

  // API Functions
  const fetchWorkflow = async (id: string): Promise<WorkflowData | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/workflows/${id}`, {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
          "Content-Type": "application/json",
        },
      });
      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch (error) {
      console.error("Failed to fetch workflow:", error);
      return null;
    }
  };

  const fetchAvailableAgents = async (): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/agents`, {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });
      if (response.ok) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch agents:", error);
      return [];
    }
  };

  const fetchAvailableTools = async (): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/tools`, {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });
      if (response.ok) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch tools:", error);
      return [];
    }
  };

  const fetchWorkflowTemplates = async (): Promise<WorkflowTemplate[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/workflow-templates`, {
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });
      if (response.ok) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch templates:", error);
      return [];
    }
  };

  const fetchExecutionHistory = async (
    workflowId: string,
  ): Promise<WorkflowExecution[]> => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/workflows/${workflowId}/executions`,
        {
          headers: {
            Authorization: `Bearer ${mockAuthContext.token}`,
          },
        },
      );
      if (response.ok) {
        return await response.json();
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch execution history:", error);
      return [];
    }
  };

  const saveWorkflow = async (data: WorkflowData): Promise<boolean> => {
    try {
      const method = workflowId ? "PUT" : "POST";
      const url = workflowId
        ? `${API_BASE_URL}/workflows/${workflowId}`
        : `${API_BASE_URL}/workflows`;

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          updatedAt: new Date().toISOString(),
        }),
      });

      return response.ok;
    } catch (error) {
      console.error("Failed to save workflow:", error);
      return false;
    }
  };

  const executeWorkflow = async (
    data: WorkflowData,
    input: any = {},
  ): Promise<WorkflowExecution | null> => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/workflows/${data.id}/execute`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${mockAuthContext.token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ input }),
        },
      );

      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch (error) {
      console.error("Failed to execute workflow:", error);
      return null;
    }
  };

  const validateWorkflow = (data: WorkflowData): string[] => {
    const errors: string[] = [];

    if (!data.name.trim()) {
      errors.push("Workflow name is required");
    }

    if (data.nodes.length < 2) {
      errors.push("Workflow must have at least a start and end node");
    }

    const startNodes = data.nodes.filter((n) => n.type === "start");
    const endNodes = data.nodes.filter((n) => n.type === "end");

    if (startNodes.length !== 1) {
      errors.push("Workflow must have exactly one start node");
    }

    if (endNodes.length === 0) {
      errors.push("Workflow must have at least one end node");
    }

    // Check for disconnected nodes
    const connectedNodes = new Set<string>();
    const queue = startNodes.map((n) => n.id);

    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      if (connectedNodes.has(nodeId)) continue;

      connectedNodes.add(nodeId);
      const node = data.nodes.find((n) => n.id === nodeId);
      if (node) {
        node.connections.forEach((conn) => {
          if (!connectedNodes.has(conn.targetId)) {
            queue.push(conn.targetId);
          }
        });
      }
    }

    const disconnectedNodes = data.nodes.filter(
      (n) => !connectedNodes.has(n.id) && n.type !== "start",
    );
    if (disconnectedNodes.length > 0) {
      errors.push(
        `Disconnected nodes found: ${disconnectedNodes.map((n) => n.name).join(", ")}`,
      );
    }

    return errors;
  };

  const handleRealtimeEvent = useCallback(
    (event: any) => {
      setRealtimeEvents((prev) => [event, ...prev.slice(0, 99)]); // Keep last 100 events

      switch (event.type) {
        case "step_completed":
          if (event.data.hybridId === workflowData.id) {
            setExecutionLog((prev) => [
              ...prev,
              `${new Date().toLocaleTimeString()} - Step ${event.data.stepNumber}: ${event.data.stepType} completed in ${event.data.duration}ms`,
            ]);

            // Update node status
            setWorkflowData((prev) => ({
              ...prev,
              nodes: prev.nodes.map((node) =>
                node.id === event.data.stepId
                  ? {
                      ...node,
                      status: "completed",
                      lastExecuted: new Date().toISOString(),
                    }
                  : node,
              ),
            }));
          }
          break;
        case "finished":
          if (event.data.hybridId === workflowData.id) {
            setIsExecuting(false);
            setExecutionLog((prev) => [
              ...prev,
              `${new Date().toLocaleTimeString()} - Workflow completed successfully in ${event.data.totalDuration}ms`,
            ]);

            // Update workflow statistics
            setWorkflowData((prev) => ({
              ...prev,
              lastExecutedAt: new Date().toISOString(),
              executionCount: prev.executionCount + 1,
              avgExecutionTime:
                (prev.avgExecutionTime * prev.executionCount +
                  event.data.totalDuration) /
                (prev.executionCount + 1),
              totalCost: prev.totalCost + (event.data.cost || 0),
            }));
          }
          break;
        case "execution_failed":
          if (event.data.hybridId === workflowData.id) {
            setIsExecuting(false);
            setExecutionLog((prev) => [
              ...prev,
              `${new Date().toLocaleTimeString()} - Workflow failed at step ${event.data.failedStep}: ${event.data.error}`,
            ]);

            // Update failed node status
            setWorkflowData((prev) => ({
              ...prev,
              nodes: prev.nodes.map((node) =>
                node.id === event.data.failedStep
                  ? {
                      ...node,
                      status: "failed",
                      lastError: event.data.error,
                      errorCount: (node.errorCount || 0) + 1,
                    }
                  : node,
              ),
            }));
          }
          break;
        case "node_updated":
          if (event.data.workflowId === workflowData.id) {
            // Handle collaborative editing
            setWorkflowData((prev) => ({
              ...prev,
              nodes: prev.nodes.map((node) =>
                node.id === event.data.nodeId
                  ? { ...node, ...event.data.updates }
                  : node,
              ),
            }));
          }
          break;
        case "workflow_saved":
          if (
            event.data.workflowId === workflowData.id &&
            event.userId !== user?.id
          ) {
            // Another user saved the workflow
            setExecutionLog((prev) => [
              ...prev,
              `${new Date().toLocaleTimeString()} - Workflow updated by ${event.data.userName}`,
            ]);
          }
          break;
      }
    },
    [workflowData.id, user?.id],
  );

  const handleAnalyticsEvent = useCallback(
    (event: any) => {
      if (
        event.type === "metrics_updated" &&
        event.data.workflowId === workflowData.id
      ) {
        // Update workflow metrics in real-time
        setWorkflowData((prev) => ({
          ...prev,
          successRate: event.data.successRate,
          avgExecutionTime: event.data.avgExecutionTime,
          totalCost: event.data.totalCost,
        }));
      }
    },
    [workflowData.id],
  );

  const addNode = (
    type: WorkflowNode["type"],
    position: { x: number; y: number },
    template?: Partial<WorkflowNode>,
  ) => {
    const newNode: WorkflowNode = {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      name:
        template?.name ||
        `${type.charAt(0).toUpperCase() + type.slice(1)} ${workflowData.nodes.length}`,
      description: template?.description || "",
      position,
      config: {
        enabled: true,
        priority: 1,
        tags: [],
        metadata: {},
        ...template?.config,
      },
      connections: template?.connections || [],
      status: "idle",
      executionCount: 0,
      errorCount: 0,
    };

    setWorkflowData((prev) => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      updatedAt: new Date().toISOString(),
    }));

    setUnsavedChanges(true);

    // Broadcast node addition for collaborative editing
    if (isConnected && user && organization) {
      const apixClient = getAPXClient(
        organization.id,
        user.id,
        mockAuthContext.token!,
      );
      apixClient.sendEvent({
        id: `node-added-${Date.now()}`,
        type: "node_added",
        module: "hybrids",
        organizationId: organization.id,
        userId: user.id,
        timestamp: new Date().toISOString(),
        data: {
          workflowId: workflowData.id,
          node: newNode,
        },
        version: 1,
      });
    }
  };

  const addNodeFromTemplate = (template: WorkflowTemplate) => {
    const centerX = canvasRef.current ? canvasRef.current.clientWidth / 2 : 400;
    const centerY = canvasRef.current
      ? canvasRef.current.clientHeight / 2
      : 300;

    const offsetNodes = template.nodes.map((node) => ({
      ...node,
      id: `${node.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      position: {
        x: node.position.x + centerX - 200,
        y: node.position.y + centerY - 150,
      },
    }));

    setWorkflowData((prev) => ({
      ...prev,
      nodes: [...prev.nodes, ...offsetNodes],
      variables: { ...prev.variables, ...template.variables },
      settings: { ...prev.settings, ...template.settings },
      updatedAt: new Date().toISOString(),
    }));

    setUnsavedChanges(true);
    setShowTemplateLibrary(false);
  };

  const updateNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === nodeId ? { ...node, ...updates } : node,
      ),
      updatedAt: new Date().toISOString(),
    }));

    setUnsavedChanges(true);

    // Broadcast node update for collaborative editing
    if (isConnected && user && organization) {
      const apixClient = getAPXClient(
        organization.id,
        user.id,
        mockAuthContext.token!,
      );
      apixClient.sendEvent({
        id: `node-updated-${Date.now()}`,
        type: "node_updated",
        module: "hybrids",
        organizationId: organization.id,
        userId: user.id,
        timestamp: new Date().toISOString(),
        data: {
          workflowId: workflowData.id,
          nodeId,
          updates,
        },
        version: 1,
      });
    }
  };

  const deleteNode = (nodeId: string) => {
    if (nodeId === "start" || nodeId === "end") return;
    if (mode === "view") return;

    // Remove connections to this node
    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes
        .filter((node) => node.id !== nodeId)
        .map((node) => ({
          ...node,
          connections: node.connections.filter(
            (conn) => conn.targetId !== nodeId,
          ),
        })),
      updatedAt: new Date().toISOString(),
    }));

    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }

    setUnsavedChanges(true);

    // Broadcast node deletion for collaborative editing
    if (isConnected && user && organization) {
      const apixClient = getAPXClient(
        organization.id,
        user.id,
        mockAuthContext.token!,
      );
      apixClient.sendEvent({
        id: `node-deleted-${Date.now()}`,
        type: "node_deleted",
        module: "hybrids",
        organizationId: organization.id,
        userId: user.id,
        timestamp: new Date().toISOString(),
        data: {
          workflowId: workflowData.id,
          nodeId,
        },
        version: 1,
      });
    }
  };

  const connectNodes = (
    fromId: string,
    toId: string,
    condition?: string,
    label?: string,
  ) => {
    if (mode === "view") return;

    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === fromId
          ? {
              ...node,
              connections: [
                ...node.connections.filter((conn) => conn.targetId !== toId),
                { targetId: toId, condition, label },
              ],
            }
          : node,
      ),
      updatedAt: new Date().toISOString(),
    }));

    setUnsavedChanges(true);
  };

  const disconnectNodes = (fromId: string, toId: string) => {
    if (mode === "view") return;

    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === fromId
          ? {
              ...node,
              connections: node.connections.filter(
                (conn) => conn.targetId !== toId,
              ),
            }
          : node,
      ),
      updatedAt: new Date().toISOString(),
    }));

    setUnsavedChanges(true);
  };

  const handleSave = async () => {
    if (!canCreateWorkflow && !canEditWorkflow) {
      setError("You do not have permission to save workflows.");
      return;
    }

    if (validationErrors.length > 0) {
      setError(`Cannot save workflow: ${validationErrors.join(", ")}`);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const success = await saveWorkflow(workflowData);

      if (!success) {
        throw new Error("Failed to save workflow to server");
      }

      // Update session with current state
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          builderState: workflowData,
          lastSaved: new Date().toISOString(),
        });
      }

      // Broadcast save event via APIX
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `workflow-saved-${Date.now()}`,
          type: workflowId ? "workflow_updated" : "workflow_created",
          module: "hybrids",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            workflowId: workflowData.id,
            workflowName: workflowData.name,
            nodeCount: workflowData.nodes.length,
            userName: user.name,
          },
          version: 1,
        });
      }

      setUnsavedChanges(false);
      setExecutionLog((prev) => [
        ...prev,
        `${new Date().toLocaleTimeString()} - Workflow saved successfully`,
      ]);

      if (onSave) {
        await onSave(workflowData);
      }
    } catch (error) {
      console.error("Error saving workflow:", error);
      setError(
        `Error saving workflow: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAutoSave = async () => {
    if (mode === "view" || !canEditWorkflow) return;

    try {
      const success = await saveWorkflow(workflowData);
      if (success) {
        setUnsavedChanges(false);
        setExecutionLog((prev) => [
          ...prev,
          `${new Date().toLocaleTimeString()} - Auto-saved`,
        ]);
      }
    } catch (error) {
      console.error("Auto-save failed:", error);
    }
  };

  const handleExecute = async (input: any = {}) => {
    if (!canExecuteWorkflow) {
      setError("You do not have permission to execute workflows.");
      return;
    }

    if (validationErrors.length > 0) {
      setError(`Cannot execute workflow: ${validationErrors.join(", ")}`);
      return;
    }

    setIsExecuting(true);
    setExecutionLog([
      `${new Date().toLocaleTimeString()} - Starting workflow execution...`,
    ]);
    setError(null);

    try {
      // Create execution session
      const executionSessionId = await sessionManager.createSession(
        "hybrids",
        "hybrid",
        {
          workflowConfig: workflowData,
          startTime: new Date().toISOString(),
          input,
        },
        {
          parentSessionId: sessionId,
          tags: ["execution", "hybrid-workflow"],
          conversationId: `execution-${Date.now()}`,
        },
      );

      // Execute workflow via API
      const execution = await executeWorkflow(workflowData, input);

      if (!execution) {
        throw new Error("Failed to start workflow execution");
      }

      setCurrentExecution(execution);
      setExecutionHistory((prev) => [execution, ...prev]);

      // Broadcast execution start event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `hybrid-started-${Date.now()}`,
          type: "hybrid_started",
          module: "hybrids",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            hybridId: workflowData.id,
            executionId: execution.id,
            workflowId: workflowData.id,
            workflowName: workflowData.name,
            agentNodes: workflowData.nodes.filter((n) => n.type === "agent")
              .length,
            toolNodes: workflowData.nodes.filter((n) => n.type === "tool")
              .length,
            input,
            expectedSteps: workflowData.nodes.length - 2, // Exclude start/end
            triggeredBy: user.id,
          },
          version: 1,
        });
      }

      // Update workflow nodes status
      setWorkflowData((prev) => ({
        ...prev,
        nodes: prev.nodes.map((node) => ({
          ...node,
          status: node.type === "start" ? "running" : "idle",
        })),
      }));

      if (onExecute) {
        await onExecute(workflowData, input);
      }
    } catch (error) {
      console.error("Error executing workflow:", error);
      setExecutionLog((prev) => [
        ...prev,
        `${new Date().toLocaleTimeString()} - Execution failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      ]);
      setError(
        `Execution failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      setIsExecuting(false);
    }
  };

  const handleStopExecution = async () => {
    if (!currentExecution) return;

    try {
      const response = await fetch(
        `${API_BASE_URL}/workflows/${workflowData.id}/executions/${currentExecution.id}/stop`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${mockAuthContext.token}`,
          },
        },
      );

      if (response.ok) {
        setIsExecuting(false);
        setExecutionLog((prev) => [
          ...prev,
          `${new Date().toLocaleTimeString()} - Execution stopped by user`,
        ]);

        // Reset node statuses
        setWorkflowData((prev) => ({
          ...prev,
          nodes: prev.nodes.map((node) => ({
            ...node,
            status: "idle",
          })),
        }));
      }
    } catch (error) {
      console.error("Failed to stop execution:", error);
      setError("Failed to stop execution");
    }
  };

  const handleDelete = async () => {
    if (!canDeleteWorkflow || !workflowId) {
      setError("You do not have permission to delete this workflow.");
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this workflow? This action cannot be undone.",
      )
    ) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/workflows/${workflowId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${mockAuthContext.token}`,
        },
      });

      if (response.ok) {
        if (onDelete) {
          await onDelete(workflowId);
        }
      } else {
        throw new Error("Failed to delete workflow");
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
      setError("Failed to delete workflow");
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (format: "json" | "yaml" | "code") => {
    try {
      if (onExport) {
        const exportData = await onExport(workflowData.id, format);

        // Download the exported data
        const blob = new Blob([exportData], {
          type: format === "json" ? "application/json" : "text/plain",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${workflowData.name}.${format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Export failed:", error);
      setError("Failed to export workflow");
    }
  };

  const handleImport = async (file: File) => {
    try {
      const text = await file.text();
      const format =
        file.name.endsWith(".yaml") || file.name.endsWith(".yml")
          ? "yaml"
          : "json";

      if (onImport) {
        const importedData = await onImport(text, format);
        setWorkflowData(importedData);
        setUnsavedChanges(true);
      }
    } catch (error) {
      console.error("Import failed:", error);
      setError("Failed to import workflow");
    }
  };

  const getNodeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return <Bot className="h-4 w-4" />;
      case "tool":
        return <Wrench className="h-4 w-4" />;
      case "condition":
        return <Diamond className="h-4 w-4" />;
      case "start":
        return <Circle className="h-4 w-4" />;
      case "end":
        return <Square className="h-4 w-4" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  const getNodeColor = (type: string) => {
    switch (type) {
      case "agent":
        return "bg-blue-100 border-blue-300 text-blue-700";
      case "tool":
        return "bg-green-100 border-green-300 text-green-700";
      case "condition":
        return "bg-yellow-100 border-yellow-300 text-yellow-700";
      case "start":
        return "bg-gray-100 border-gray-300 text-gray-700";
      case "end":
        return "bg-red-100 border-red-300 text-red-700";
      default:
        return "bg-gray-100 border-gray-300 text-gray-700";
    }
  };

  return (
    <div className="bg-background w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">
              {workflowId ? "Edit Workflow" : "Create New Workflow"}
            </h1>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    {isConnected ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>
              <Badge variant="secondary">{organization?.name}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-4 mt-1">
            <Input
              value={workflowData.name}
              onChange={(e) =>
                setWorkflowData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Workflow name..."
              className="w-64"
            />
            <Input
              value={workflowData.description}
              onChange={(e) =>
                setWorkflowData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Description..."
              className="w-96"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={!canCreateWorkflow && !canEditWorkflow}
          >
            <Save className="mr-2 h-4 w-4" /> Save Workflow
          </Button>
          <Button
            onClick={handleExecute}
            disabled={isExecuting || !canExecuteWorkflow}
            variant="default"
          >
            {isExecuting ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Executing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" /> Execute
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Node Palette */}
        <div className="w-64 border-r p-4 space-y-4">
          <h3 className="font-semibold">Node Types</h3>
          <div className="space-y-2">
            {[
              {
                type: "agent",
                label: "AI Agent",
                icon: <Bot className="h-4 w-4" />,
              },
              {
                type: "tool",
                label: "Tool",
                icon: <Wrench className="h-4 w-4" />,
              },
              {
                type: "condition",
                label: "Condition",
                icon: <Diamond className="h-4 w-4" />,
              },
            ].map(({ type, label, icon }) => (
              <Button
                key={type}
                variant="outline"
                className="w-full justify-start"
                onClick={() => addNode(type as any, { x: 200, y: 200 })}
              >
                {icon}
                <span className="ml-2">{label}</span>
              </Button>
            ))}
          </div>

          {/* Execution Log */}
          {executionLog.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Execution Log</h3>
              <ScrollArea className="h-32 border rounded p-2">
                <div className="space-y-1">
                  {executionLog.map((log, index) => (
                    <div key={index} className="text-xs text-muted-foreground">
                      {log}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        {/* Canvas */}
        <div className="flex-1 relative overflow-auto bg-gray-50">
          <div className="absolute inset-0 p-4">
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              {/* Grid pattern */}
              <defs>
                <pattern
                  id="grid"
                  width="20"
                  height="20"
                  patternUnits="userSpaceOnUse"
                >
                  <path
                    d="M 20 0 L 0 0 0 20"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="1"
                  />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />

              {/* Connection lines */}
              {workflowData.nodes
                .map((node) =>
                  node.connections.map((targetId) => {
                    const target = workflowData.nodes.find(
                      (n) => n.id === targetId,
                    );
                    if (!target) return null;
                    return (
                      <line
                        key={`${node.id}-${targetId}`}
                        x1={node.position.x + 60}
                        y1={node.position.y + 30}
                        x2={target.position.x + 60}
                        y2={target.position.y + 30}
                        stroke="#6b7280"
                        strokeWidth="2"
                        markerEnd="url(#arrowhead)"
                      />
                    );
                  }),
                )
                .flat()}

              {/* Arrow marker */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                </marker>
              </defs>
            </svg>

            {/* Nodes */}
            {workflowData.nodes.map((node) => (
              <div
                key={node.id}
                className={`absolute w-32 h-16 border-2 rounded-lg p-2 cursor-pointer transition-all hover:shadow-md ${
                  selectedNode?.id === node.id ? "ring-2 ring-blue-500" : ""
                } ${getNodeColor(node.type)}`}
                style={{
                  left: node.position.x,
                  top: node.position.y,
                }}
                onClick={() => setSelectedNode(node)}
              >
                <div className="flex items-center gap-2">
                  {getNodeIcon(node.type)}
                  <span className="text-xs font-medium truncate">
                    {node.name}
                  </span>
                </div>
                <div className="text-xs opacity-75 capitalize">{node.type}</div>
                {node.id !== "start" && node.id !== "end" && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 text-white hover:bg-red-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteNode(node.id);
                    }}
                  >
                    <Trash className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Properties Panel */}
        {selectedNode && (
          <div className="w-80 border-l p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Node Properties</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedNode(null)}
              >
                ×
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getNodeIcon(selectedNode.type)}
                  {selectedNode.type.charAt(0).toUpperCase() +
                    selectedNode.type.slice(1)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Name</Label>
                  <Input
                    value={selectedNode.name}
                    onChange={(e) =>
                      updateNode(selectedNode.id, { name: e.target.value })
                    }
                  />
                </div>

                {selectedNode.type === "agent" && (
                  <div className="space-y-2">
                    <Label>Agent Configuration</Label>
                    <div className="text-sm text-muted-foreground">
                      Configure the AI agent settings, model, and prompt
                      template.
                    </div>
                    <Button variant="outline" size="sm">
                      <Settings className="mr-2 h-4 w-4" /> Configure Agent
                    </Button>
                  </div>
                )}

                {selectedNode.type === "tool" && (
                  <div className="space-y-2">
                    <Label>Tool Configuration</Label>
                    <div className="text-sm text-muted-foreground">
                      Select and configure the tool to execute.
                    </div>
                    <Button variant="outline" size="sm">
                      <Wrench className="mr-2 h-4 w-4" /> Select Tool
                    </Button>
                  </div>
                )}

                {selectedNode.type === "condition" && (
                  <div className="space-y-2">
                    <Label>Condition Logic</Label>
                    <div className="text-sm text-muted-foreground">
                      Define the conditional logic for branching.
                    </div>
                    <Button variant="outline" size="sm">
                      <GitBranch className="mr-2 h-4 w-4" /> Configure Logic
                    </Button>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Position</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      value={selectedNode.position.x}
                      onChange={(e) =>
                        updateNode(selectedNode.id, {
                          position: {
                            ...selectedNode.position,
                            x: parseInt(e.target.value),
                          },
                        })
                      }
                      placeholder="X"
                    />
                    <Input
                      type="number"
                      value={selectedNode.position.y}
                      onChange={(e) =>
                        updateNode(selectedNode.id, {
                          position: {
                            ...selectedNode.position,
                            y: parseInt(e.target.value),
                          },
                        })
                      }
                      placeholder="Y"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Connections</Label>
                  <div className="text-sm text-muted-foreground">
                    Connected to: {selectedNode.connections.length} nodes
                  </div>
                  {selectedNode.connections.map((connId) => {
                    const connectedNode = workflowData.nodes.find(
                      (n) => n.id === connId,
                    );
                    return connectedNode ? (
                      <Badge key={connId} variant="outline">
                        {connectedNode.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowBuilder;
