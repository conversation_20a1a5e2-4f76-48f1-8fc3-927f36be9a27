"use client";

import React from "react";
import { mockAuthContext } from "@/lib/auth-context";

export default function TestNavigationPage() {
  const { hasPermission, user } = mockAuthContext;

  const testPermissions = [
    { module: "agents", action: "read" },
    { module: "tools", action: "read" },
    { module: "hybrids", action: "read" },
    { module: "analytics", action: "read" },
    { module: "sessions", action: "read" },
    { module: "knowledge", action: "read" },
    { module: "widgets", action: "read" },
    { module: "sandbox", action: "read" },
    { module: "users", action: "read" },
    { module: "settings", action: "read" },
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Navigation Permission Test</h1>
      <p className="mb-4">User Role: {user?.role}</p>
      
      <div className="space-y-2">
        {testPermissions.map((perm) => (
          <div key={`${perm.module}-${perm.action}`} className="flex items-center space-x-2">
            <span className={`w-4 h-4 rounded ${hasPermission(perm.module, perm.action) ? 'bg-green-500' : 'bg-red-500'}`}></span>
            <span>{perm.module} - {perm.action}: {hasPermission(perm.module, perm.action) ? 'ALLOWED' : 'DENIED'}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
