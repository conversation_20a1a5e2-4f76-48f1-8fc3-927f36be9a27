"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Activity,
  Search,
  Filter,
  MoreHorizontal,
  Clock,
  User,
  Bot,
  Wrench,
  Network,
  RefreshCw,
  Trash2,
  Eye,
  Play,
  Pause,
  StopCircle,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";

interface Session {
  id: string;
  userId: string;
  userName: string;
  type: "agent" | "tool" | "hybrid";
  module: string;
  status: "active" | "idle" | "completed" | "error";
  startTime: string;
  lastActivity: string;
  duration: string;
  executionCount: number;
  memoryUsage: number;
}

export default function SessionsPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, hasPermission } = mockAuthContext;

  const canManageSessions = hasPermission("sessions", "update");
  const canDeleteSessions = hasPermission("sessions", "delete");

  // Mock session data
  useEffect(() => {
    const mockSessions: Session[] = [
      {
        id: "sess_1",
        userId: "user_1",
        userName: "John Doe",
        type: "agent",
        module: "Customer Support Agent",
        status: "active",
        startTime: "2024-01-15T10:30:00Z",
        lastActivity: "2024-01-15T11:45:00Z",
        duration: "1h 15m",
        executionCount: 23,
        memoryUsage: 45,
      },
      {
        id: "sess_2",
        userId: "user_2",
        userName: "Jane Smith",
        type: "tool",
        module: "Email Sender",
        status: "idle",
        startTime: "2024-01-15T09:15:00Z",
        lastActivity: "2024-01-15T11:30:00Z",
        duration: "2h 15m",
        executionCount: 8,
        memoryUsage: 12,
      },
      {
        id: "sess_3",
        userId: "user_3",
        userName: "Bob Wilson",
        type: "hybrid",
        module: "Data Analysis Workflow",
        status: "active",
        startTime: "2024-01-15T11:00:00Z",
        lastActivity: "2024-01-15T11:44:00Z",
        duration: "44m",
        executionCount: 15,
        memoryUsage: 78,
      },
      {
        id: "sess_4",
        userId: "user_4",
        userName: "Alice Brown",
        type: "agent",
        module: "Content Generator",
        status: "completed",
        startTime: "2024-01-15T08:30:00Z",
        lastActivity: "2024-01-15T10:15:00Z",
        duration: "1h 45m",
        executionCount: 12,
        memoryUsage: 0,
      },
      {
        id: "sess_5",
        userId: "user_5",
        userName: "Charlie Davis",
        type: "tool",
        module: "Database Query",
        status: "error",
        startTime: "2024-01-15T11:20:00Z",
        lastActivity: "2024-01-15T11:25:00Z",
        duration: "5m",
        executionCount: 2,
        memoryUsage: 0,
      },
    ];
    setSessions(mockSessions);
  }, []);

  const filteredSessions = sessions.filter((session) => {
    const matchesSearch = 
      session.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.module.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = 
      activeTab === "all" || 
      session.status === activeTab ||
      session.type === activeTab;
    
    return matchesSearch && matchesTab;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "idle":
        return "bg-yellow-500";
      case "completed":
        return "bg-blue-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return Bot;
      case "tool":
        return Wrench;
      case "hybrid":
        return Network;
      default:
        return Activity;
    }
  };

  const handleSessionAction = (sessionId: string, action: string) => {
    console.log(`${action} session ${sessionId}`);
    // Implement session management actions
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sessions</h1>
          <p className="text-muted-foreground">
            Monitor and manage active AI sessions in real-time
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setIsLoading(!isLoading)}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sessions.filter(s => s.status === "active").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessions.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1h 24m</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(sessions.reduce((acc, s) => acc + s.memoryUsage, 0) / sessions.length)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sessions Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Session Management</CardTitle>
              <CardDescription>
                View and manage all active and recent sessions
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search sessions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-[250px]"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">All Sessions</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="idle">Idle</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="error">Error</TabsTrigger>
            </TabsList>
            <TabsContent value={activeTab} className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Session</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Module</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Executions</TableHead>
                    <TableHead>Memory</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSessions.map((session) => {
                    const TypeIcon = getTypeIcon(session.type);
                    return (
                      <TableRow key={session.id}>
                        <TableCell className="font-medium">
                          {session.id}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <User className="h-4 w-4" />
                            <span>{session.userName}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4" />
                            <span className="capitalize">{session.type}</span>
                          </div>
                        </TableCell>
                        <TableCell>{session.module}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="flex items-center space-x-1 w-fit">
                            <div className={`h-2 w-2 rounded-full ${getStatusColor(session.status)}`} />
                            <span className="capitalize">{session.status}</span>
                          </Badge>
                        </TableCell>
                        <TableCell>{session.duration}</TableCell>
                        <TableCell>{session.executionCount}</TableCell>
                        <TableCell>{session.memoryUsage}%</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleSessionAction(session.id, "view")}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {canManageSessions && session.status === "active" && (
                                <DropdownMenuItem onClick={() => handleSessionAction(session.id, "pause")}>
                                  <Pause className="mr-2 h-4 w-4" />
                                  Pause Session
                                </DropdownMenuItem>
                              )}
                              {canManageSessions && session.status === "idle" && (
                                <DropdownMenuItem onClick={() => handleSessionAction(session.id, "resume")}>
                                  <Play className="mr-2 h-4 w-4" />
                                  Resume Session
                                </DropdownMenuItem>
                              )}
                              {canManageSessions && (
                                <DropdownMenuItem onClick={() => handleSessionAction(session.id, "stop")}>
                                  <StopCircle className="mr-2 h-4 w-4" />
                                  Stop Session
                                </DropdownMenuItem>
                              )}
                              {canDeleteSessions && (
                                <DropdownMenuItem 
                                  onClick={() => handleSessionAction(session.id, "delete")}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Session
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
