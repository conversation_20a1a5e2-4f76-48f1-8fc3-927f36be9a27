"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BookOpen,
  Upload,
  Search,
  Filter,
  Plus,
  FileText,
  Globe,
  Database,
  Trash2,
  Edit,
  Eye,
  Download,
  RefreshCw,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";

interface KnowledgeSource {
  id: string;
  name: string;
  type: "file" | "url" | "text" | "database";
  status: "processing" | "ready" | "error";
  size: string;
  chunks: number;
  lastUpdated: string;
  tags: string[];
}

export default function KnowledgePage() {
  const [activeTab, setActiveTab] = useState("sources");
  const [searchQuery, setSearchQuery] = useState("");
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [uploadType, setUploadType] = useState<"file" | "url" | "text">("file");
  const { hasPermission } = mockAuthContext;

  const canCreateKnowledge = hasPermission("knowledge", "create");
  const canUpdateKnowledge = hasPermission("knowledge", "update");
  const canDeleteKnowledge = hasPermission("knowledge", "delete");

  const knowledgeSources: KnowledgeSource[] = [
    {
      id: "kb_1",
      name: "Product Documentation",
      type: "file",
      status: "ready",
      size: "2.4 MB",
      chunks: 156,
      lastUpdated: "2024-01-15T10:30:00Z",
      tags: ["documentation", "product"],
    },
    {
      id: "kb_2",
      name: "Company Website",
      type: "url",
      status: "ready",
      size: "1.8 MB",
      chunks: 89,
      lastUpdated: "2024-01-14T16:45:00Z",
      tags: ["website", "public"],
    },
    {
      id: "kb_3",
      name: "FAQ Database",
      type: "database",
      status: "processing",
      size: "3.2 MB",
      chunks: 0,
      lastUpdated: "2024-01-15T11:00:00Z",
      tags: ["faq", "support"],
    },
    {
      id: "kb_4",
      name: "Training Manual",
      type: "text",
      status: "ready",
      size: "856 KB",
      chunks: 45,
      lastUpdated: "2024-01-13T09:15:00Z",
      tags: ["training", "manual"],
    },
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "file":
        return FileText;
      case "url":
        return Globe;
      case "database":
        return Database;
      case "text":
        return BookOpen;
      default:
        return FileText;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ready":
        return "bg-green-500";
      case "processing":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Base</h1>
          <p className="text-muted-foreground">
            Manage documents and data sources for RAG-powered AI agents
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {canCreateKnowledge && (
            <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Source
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Add Knowledge Source</DialogTitle>
                  <DialogDescription>
                    Upload files, add URLs, or input text to expand your knowledge base.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="source-type">Source Type</Label>
                    <Select value={uploadType} onValueChange={(value: any) => setUploadType(value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select source type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="file">File Upload</SelectItem>
                        <SelectItem value="url">Website URL</SelectItem>
                        <SelectItem value="text">Direct Text</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {uploadType === "file" && (
                    <div>
                      <Label htmlFor="file-upload">Upload File</Label>
                      <Input id="file-upload" type="file" accept=".pdf,.txt,.docx,.md" />
                    </div>
                  )}

                  {uploadType === "url" && (
                    <div>
                      <Label htmlFor="url-input">Website URL</Label>
                      <Input id="url-input" placeholder="https://example.com" />
                    </div>
                  )}

                  {uploadType === "text" && (
                    <div>
                      <Label htmlFor="text-input">Text Content</Label>
                      <Textarea 
                        id="text-input" 
                        placeholder="Paste your text content here..."
                        rows={6}
                      />
                    </div>
                  )}

                  <div>
                    <Label htmlFor="source-name">Source Name</Label>
                    <Input id="source-name" placeholder="Enter a descriptive name" />
                  </div>

                  <div>
                    <Label htmlFor="tags">Tags (comma-separated)</Label>
                    <Input id="tags" placeholder="documentation, product, support" />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsUploadOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={() => setIsUploadOpen(false)}>
                      <Upload className="h-4 w-4 mr-2" />
                      Add Source
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sources</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{knowledgeSources.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Chunks</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {knowledgeSources.reduce((acc, source) => acc + source.chunks, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8.2 MB</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready Sources</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {knowledgeSources.filter(s => s.status === "ready").length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="sources">Knowledge Sources</TabsTrigger>
          <TabsTrigger value="search">Search & Test</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="sources" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Knowledge Sources</CardTitle>
                  <CardDescription>
                    Manage your uploaded documents and data sources
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search sources..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8 w-[250px]"
                    />
                  </div>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {knowledgeSources.map((source) => {
                  const TypeIcon = getTypeIcon(source.type);
                  return (
                    <Card key={source.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4" />
                            <span className="font-medium">{source.name}</span>
                          </div>
                          <Badge variant="outline" className="flex items-center space-x-1">
                            <div className={`h-2 w-2 rounded-full ${getStatusColor(source.status)}`} />
                            <span className="capitalize">{source.status}</span>
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2 text-sm text-muted-foreground">
                          <div className="flex justify-between">
                            <span>Size:</span>
                            <span>{source.size}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Chunks:</span>
                            <span>{source.chunks}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Updated:</span>
                            <span>{new Date(source.lastUpdated).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-1 mt-3">
                          {source.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex justify-between mt-4">
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                            {canUpdateKnowledge && (
                              <Button variant="ghost" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                            )}
                            <Button variant="ghost" size="sm">
                              <Download className="h-3 w-3" />
                            </Button>
                          </div>
                          {canDeleteKnowledge && (
                            <Button variant="ghost" size="sm" className="text-red-600">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Search & Test Knowledge Base</CardTitle>
              <CardDescription>
                Test your knowledge base by searching for information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Input 
                    placeholder="Ask a question or search for information..."
                    className="flex-1"
                  />
                  <Button>
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>
                <div className="border rounded-lg p-4 min-h-[200px] bg-muted/50">
                  <p className="text-muted-foreground text-center">
                    Search results will appear here...
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Knowledge Base Settings</CardTitle>
              <CardDescription>
                Configure how your knowledge base processes and retrieves information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="chunk-size">Chunk Size</Label>
                  <Select defaultValue="1000">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="500">500 characters</SelectItem>
                      <SelectItem value="1000">1000 characters</SelectItem>
                      <SelectItem value="2000">2000 characters</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="overlap">Chunk Overlap</Label>
                  <Select defaultValue="100">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50 characters</SelectItem>
                      <SelectItem value="100">100 characters</SelectItem>
                      <SelectItem value="200">200 characters</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="similarity-threshold">Similarity Threshold</Label>
                  <Select defaultValue="0.7">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.5">0.5 (Low)</SelectItem>
                      <SelectItem value="0.7">0.7 (Medium)</SelectItem>
                      <SelectItem value="0.9">0.9 (High)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>Save Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
