"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON><PERSON>rovider,
  Toolt<PERSON>Trigger,
} from "@/components/ui/tooltip";
import {
  AlertCircle,
  Check,
  ChevronRight,
  Code,
  Cog,
  Copy,
  Play,
  Save,
  Settings,
  Trash,
  Shield,
  Activity,
  Database,
  Wifi,
  WifiOff,
  Plus,
  Minus,
  Key,
  Lock,
  Globe,
  TestTube,
  Zap,
  Brain,
  Network,
  DollarSign,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
} from "lucide-react";
import WorkflowBuilder from "@/components/hybrid-system/WorkflowBuilder";
import { APXClient, APXEvent, getAPXClient } from "@/lib/apix-client";
import { SessionManager } from "@/lib/session-manager";

interface Provider {
  id: string;
  name: string;
  type: "openai" | "claude" | "gemini" | "mistral" | "groq" | "custom";
  status: "active" | "inactive" | "error";
  cost: number;
  latency: number;
  reliability: number;
  capabilities: string[];
  config: Record<string, any>;
}

interface HybridWorkflow {
  id: string;
  name: string;
  description: string;
  status: "draft" | "active" | "paused";
  provider: string;
  nodes: any[];
  connections: any[];
  metrics: {
    executions: number;
    success_rate: number;
    avg_cost: number;
    avg_latency: number;
  };
}

interface SDKUsage {
  total_requests: number;
  active_connections: number;
  error_rate: number;
  avg_response_time: number;
}

export default function HybridsPage() {
  const [activeTab, setActiveTab] = useState("workflows");
  const [providers, setProviders] = useState<Provider[]>([]);
  const [workflows, setWorkflows] = useState<HybridWorkflow[]>([]);
  const [sdkUsage, setSdkUsage] = useState<SDKUsage>({
    total_requests: 0,
    active_connections: 0,
    error_rate: 0,
    avg_response_time: 0,
  });
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [apixClient, setAPXClient] = useState<APXClient | null>(null);

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeConnection = async () => {
      try {   
        // TODO: Replace these mock objects with real context or props as needed
        const organization = { id: "org-123" };
        const user = { id: "user-456" };
        const mockAuthContext = { token: "mock-token" };

        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        await apixClient.connect();
        setIsConnected(true);

        // Subscribe to hybrid system events
        apixClient.subscribe("hybrid.workflow.created", ["hybrid.workflow.created"], handleWorkflowCreated, {
          filters: {
            organizationId: organization.id,
          },
        });
        apixClient.subscribe(
          "hybrid.workflow.executed",
          ["hybrid.workflow.executed"],
          handleWorkflowExecuted, {
            filters: {
              organizationId: organization.id,
            },
          },
        );
        apixClient.subscribe("provider.selected", ["provider.selected"], handleProviderSelected, {
          filters: {
            organizationId: organization.id,
          },
        });
        apixClient.subscribe("provider.failed", ["provider.failed"], handleProviderSelected, {
          filters: {
            organizationId: organization.id,
          },
        });
        apixClient.subscribe("sdk.usage.updated", ["sdk.usage.updated"], handleSDKUsageUpdated, {
          filters: {
            organizationId: organization.id,
          },  
        });
        // Load initial data
        await loadProviders();
        await loadWorkflows();
        await loadSDKUsage();

        handleSDKUsageUpdated({
          usage: {
            total_requests: 12847,
            active_connections: 23,
            error_rate: 2.1,
            avg_response_time: 850,
          },
        });
        setAPXClient(apixClient);
      } catch (error) {
        console.error("Failed to initialize APIX connection:", error);
        setIsConnected(false);
      }
    };

    initializeConnection();

    return () => {
      if (apixClient) {
        apixClient.disconnect();
      }
    };
  }, [apixClient]);

  const handleWorkflowCreated = (data: any) => {
    setWorkflows((prev) => [...prev, data.workflow]);
    if (apixClient) {
      apixClient.sendEvent({
        id: "1",
        type: "hybrid_workflow_created",
        module: "hybrids",
        organizationId: "org-123",
        userId: "user-456",
        timestamp: new Date().toISOString(),
        version: 1,
        data: {
          workflow_id: data.workflow.id,
          session_id: SessionManager.getSessionId("hybrids", "workflow"),
        },
      });
    }
  };

  const handleWorkflowExecuted = (data: any) => {
    setWorkflows((prev) =>
      prev.map((w) =>
        w.id === data.workflow_id
          ? { ...w, metrics: { ...w.metrics, ...data.metrics } }
          : w,
      ),
    );
  };

  const handleProviderSelected = (data: any) => {
    setSelectedProvider(data.provider_id);
    if (apixClient) {
    apixClient.sendEvent({
      id: "1",
      type: "provider.selected",
      module: "hybrids",
      organizationId: "org-123",
      userId: "user-456",
      timestamp: new Date().toISOString(),
      version: 1,
      data: {
        provider_id: data.provider_id,
        reason: data.reason,
        session_id: SessionManager.getSessionId("hybrids", "provider"),
      },
    });
    }
  };

  const handleProviderFailed = (data: any) => {
    if (apixClient) {
    setProviders((prev) =>
      prev.map((p) =>
        p.id === data.provider_id ? { ...p, status: "error" } : p
      ),
    );
    }
  };

  const handleSDKUsageUpdated = (data: any) => {
    if (apixClient) {
    setSdkUsage(data.usage);
    }
  };

  const loadProviders = async () => {
    if (apixClient) {
      apixClient.sendEvent({
        id: "1",
        type: "provider.loaded",
        module: "hybrids",
        organizationId: "org-123",
        userId: "user-456",
        timestamp: new Date().toISOString(),
        version: 1,
        data: {
          providers: [
            {
              id: "openai-1",
              name: "OpenAI GPT-4",
              type: "openai",
              status: "active",
              cost: 0.03,
              latency: 1200,
              reliability: 99.5,
              capabilities: ["text-generation", "code-generation", "reasoning"],
              config: { model: "gpt-4", temperature: 0.7 },
            },
            {
              id: "claude-1",
              name: "Claude 3 Sonnet",
              type: "claude",
              status: "active",
              cost: 0.015,
              latency: 800,
              reliability: 98.8,
              capabilities: ["text-generation", "analysis", "reasoning"],
              config: { model: "claude-3-sonnet", max_tokens: 4096 },
            },
          ],
        },
      });
    }
  };

  const loadWorkflows = async () => {
    // Mock data - replace with actual API call
    const mockWorkflows: HybridWorkflow[] = [
      {
        id: "workflow-1",
        name: "Content Generation Pipeline",
        description: "Multi-step content creation with review",
        status: "active",
        provider: "openai-1",
        nodes: [],
        connections: [],
        metrics: {
          executions: 156,
          success_rate: 94.2,
          avg_cost: 0.12,
          avg_latency: 2400,
        },
      },
      {
        id: "workflow-2",
        name: "Code Review Assistant",
        description: "Automated code analysis and suggestions",
        status: "active",
        provider: "claude-1",
        nodes: [],
        connections: [],
        metrics: {
          executions: 89,
          success_rate: 97.8,
          avg_cost: 0.08,
          avg_latency: 1800,
        },
      },
    ];
    setWorkflows(mockWorkflows);
  };

  const loadSDKUsage = async () => {
    // Mock data - replace with actual API call
    setSdkUsage({
      total_requests: 12847,
      active_connections: 23,
      error_rate: 2.1,
      avg_response_time: 850,
    });
  };

  const handleProviderTest = async (providerId: string) => {
    setLoading(true);
    try {
      // Simulate provider test
      await new Promise((resolve) => setTimeout(resolve, 2000));

      if (apixClient) {
        apixClient.sendEvent({
          id: "1",
          type: "provider.tested",
          module: "hybrids",
          organizationId: "org-123",
          userId: "user-456",
          timestamp: new Date().toISOString(),
          version: 1,
          data: {
            provider_id: providerId,
            status: "success",
            latency: Math.random() * 1000 + 500,
            session_id: SessionManager.getSessionId("hybrids", "provider"),
          },
        });
      }
    } catch (error) {
      console.error("Provider test failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleWorkflowExecute = async (workflowId: string) => {
    setLoading(true);
    try {
      if (apixClient) {
        apixClient.sendEvent({
          id: "1",
          type: "hybrid.workflow.execute",
          module: "hybrids",
          organizationId: "org-123",
          userId: "user-456",
          timestamp: new Date().toISOString(),
          version: 1,
          data: {
            workflow_id: workflowId,
            session_id: SessionManager.getSessionId("hybrids", "workflow"),
          },
        });
      }

    } catch (error) {
      console.error("Workflow execution failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const getProviderStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getProviderTypeIcon = (type: string) => {
    switch (type) {
      case "openai":
        return <Brain className="h-4 w-4" />;
      case "claude":
        return <Zap className="h-4 w-4" />;
      case "gemini":
        return <Globe className="h-4 w-4" />;
      default:
        return <Network className="h-4 w-4" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Hybrid System & Provider Management
            </h1>
            <p className="text-gray-600 mt-2">
              Orchestrate AI workflows with intelligent provider routing
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? (
                <Wifi className="h-3 w-3 mr-1" />
              ) : (
                <WifiOff className="h-3 w-3 mr-1" />
              )}
              {isConnected ? "Connected" : "Disconnected"}
            </Badge>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Connection Status Alert */}
        {!isConnected && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              APIX connection lost. Some features may not work properly. Please
              refresh the page.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="workflows">Hybrid Workflows</TabsTrigger>
            <TabsTrigger value="providers">Provider Management</TabsTrigger>
            <TabsTrigger value="sdk">Universal SDK</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Hybrid Workflows Tab */}
          <TabsContent value="workflows" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card className="bg-white">
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Network className="h-5 w-5 mr-2" />
                      Workflow Builder
                    </CardTitle>
                    <CardDescription>
                      Create and manage hybrid AI workflows with visual editor
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <WorkflowBuilder />
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <Card className="bg-white">
                  <CardHeader>
                    <CardTitle className="text-lg">Active Workflows</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="space-y-3">
                        {workflows.map((workflow) => (
                          <div
                            key={workflow.id}
                            className="p-3 border rounded-lg"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">{workflow.name}</h4>
                              <Badge
                                variant={
                                  workflow.status === "active"
                                    ? "default"
                                    : "secondary"
                                }
                              >
                                {workflow.status}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              {workflow.description}
                            </p>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div>
                                Executions: {workflow.metrics.executions}
                              </div>
                              <div>
                                Success: {workflow.metrics.success_rate}%
                              </div>
                              <div>Avg Cost: ${workflow.metrics.avg_cost}</div>
                              <div>
                                Latency: {workflow.metrics.avg_latency}ms
                              </div>
                            </div>
                            <Button
                              size="sm"
                              className="w-full mt-2"
                              onClick={() => handleWorkflowExecute(workflow.id)}
                              disabled={loading}
                            >
                              <Play className="h-3 w-3 mr-1" />
                              Execute
                            </Button>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Provider Management Tab */}
          <TabsContent value="providers" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {providers.map((provider) => (
                <Card key={provider.id} className="bg-white">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        {getProviderTypeIcon(provider.type)}
                        <span className="ml-2">{provider.name}</span>
                      </div>
                      {getProviderStatusIcon(provider.status)}
                    </CardTitle>
                    <CardDescription>
                      {provider.type.toUpperCase()} •{" "}
                      {provider.capabilities.join(", ")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <Label className="text-xs text-gray-500">
                          Cost per 1K tokens
                        </Label>
                        <div className="flex items-center">
                          <DollarSign className="h-3 w-3 mr-1" />
                          {provider.cost}
                        </div>
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500">
                          Avg Latency
                        </Label>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {provider.latency}ms
                        </div>
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500">
                          Reliability
                        </Label>
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1" />
                          {provider.reliability}%
                        </div>
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500">Status</Label>
                        <Badge
                          variant={
                            provider.status === "active"
                              ? "default"
                              : "destructive"
                          }
                        >
                          {provider.status}
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">
                        Capabilities
                      </Label>
                      <div className="flex flex-wrap gap-1">
                        {provider.capabilities.map((cap) => (
                          <Badge
                            key={cap}
                            variant="outline"
                            className="text-xs"
                          >
                            {cap}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleProviderTest(provider.id)}
                      disabled={loading}
                    >
                      <TestTube className="h-3 w-3 mr-1" />
                      Test
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-3 w-3 mr-1" />
                      Config
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Universal SDK Tab */}
          <TabsContent value="sdk" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Code className="h-5 w-5 mr-2" />
                    SDK Overview
                  </CardTitle>
                  <CardDescription>
                    Universal SDK for all SynapseAI platform capabilities
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {sdkUsage.total_requests.toLocaleString()}
                      </div>
                      <div className="text-sm text-blue-600">
                        Total Requests
                      </div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {sdkUsage.active_connections}
                      </div>
                      <div className="text-sm text-green-600">
                        Active Connections
                      </div>
                    </div>
                    <div className="p-3 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {sdkUsage.error_rate}%
                      </div>
                      <div className="text-sm text-red-600">Error Rate</div>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">
                        {sdkUsage.avg_response_time}ms
                      </div>
                      <div className="text-sm text-yellow-600">
                        Avg Response
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <h4 className="font-medium">Available SDKs</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center">
                          <Code className="h-4 w-4 mr-2" />
                          <span>JavaScript/TypeScript</span>
                        </div>
                        <Button size="sm" variant="outline">
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center">
                          <Code className="h-4 w-4 mr-2" />
                          <span>Python</span>
                        </div>
                        <Button size="sm" variant="outline">
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      <div className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 mr-2" />
                          <span>REST API</span>
                        </div>
                        <Button size="sm" variant="outline">
                          <Copy className="h-3 w-3 mr-1" />
                          Docs
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardHeader>
                  <CardTitle>SDK Playground</CardTitle>
                  <CardDescription>
                    Test SDK functionality with interactive examples
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>SDK Method</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="agent.create">
                          agent.create()
                        </SelectItem>
                        <SelectItem value="tool.build">tool.build()</SelectItem>
                        <SelectItem value="hybrid.execute">
                          hybrid.execute()
                        </SelectItem>
                        <SelectItem value="provider.select">
                          provider.select()
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Parameters</Label>
                    <Textarea
                      placeholder="Enter JSON parameters..."
                      className="h-32"
                    />
                  </div>

                  <Button className="w-full">
                    <Play className="h-4 w-4 mr-2" />
                    Execute
                  </Button>

                  <div>
                    <Label>Response</Label>
                    <div className="p-3 bg-gray-50 rounded border h-24 text-sm font-mono">
                      // Response will appear here
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Workflows</p>
                      <p className="text-2xl font-bold">{workflows.length}</p>
                    </div>
                    <Network className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Active Providers</p>
                      <p className="text-2xl font-bold">
                        {providers.filter((p) => p.status === "active").length}
                      </p>
                    </div>
                    <Shield className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">SDK Requests</p>
                      <p className="text-2xl font-bold">
                        {sdkUsage.total_requests.toLocaleString()}
                      </p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Avg Cost</p>
                      <p className="text-2xl font-bold">$0.10</p>
                    </div>
                    <DollarSign className="h-8 w-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="bg-white">
              <CardHeader>
                <CardTitle>Provider Performance</CardTitle>
                <CardDescription>
                  Real-time metrics and performance analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {providers.map((provider) => (
                    <div
                      key={provider.id}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div className="flex items-center space-x-3">
                        {getProviderTypeIcon(provider.type)}
                        <div>
                          <div className="font-medium">{provider.name}</div>
                          <div className="text-sm text-gray-500">
                            {provider.type}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <div className="font-medium">${provider.cost}</div>
                          <div className="text-gray-500">Cost</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">
                            {provider.latency}ms
                          </div>
                          <div className="text-gray-500">Latency</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">
                            {provider.reliability}%
                          </div>
                          <div className="text-gray-500">Reliability</div>
                        </div>
                        {getProviderStatusIcon(provider.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );



  
  
  
}





