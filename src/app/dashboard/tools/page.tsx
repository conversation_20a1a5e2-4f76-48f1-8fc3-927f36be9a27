import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Filter, Search, Wrench, Zap, Bot } from "lucide-react";
import { Input } from "@/components/ui/input";
import ToolBuilder from "@/components/tool-manager/ToolBuilder";

export default function ToolsPage() {
  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tools</h1>
          <p className="text-muted-foreground mt-1">
            Create, manage, and integrate API tools
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Tool
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">All Tools</TabsTrigger>
            <TabsTrigger value="api">API Tools</TabsTrigger>
            <TabsTrigger value="database">Database</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="file">File Processing</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search tools..."
                className="pl-8 w-[250px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Tools</CardTitle>
                <CardDescription>Across all categories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">42</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Active Tools</CardTitle>
                <CardDescription>Currently in use</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">38</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Executions</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">2,847</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Avg. Response Time</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">0.8s</div>
              </CardContent>
            </Card>
          </div>

          {/* Tool Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                id: "1",
                name: "SendGrid Email",
                description: "Send transactional emails via SendGrid API",
                category: "email",
                executions: 1245,
                avgResponseTime: "0.5s",
                status: "active",
                cost: "$8.42",
              },
              {
                id: "2",
                name: "Stripe Payments",
                description: "Process payments and manage subscriptions",
                category: "api",
                executions: 892,
                avgResponseTime: "1.2s",
                status: "active",
                cost: "$15.67",
              },
              {
                id: "3",
                name: "PostgreSQL Query",
                description: "Execute database queries and operations",
                category: "database",
                executions: 2156,
                avgResponseTime: "0.3s",
                status: "active",
                cost: "$3.21",
              },
              {
                id: "4",
                name: "PDF Generator",
                description: "Generate PDF documents from templates",
                category: "file",
                executions: 456,
                avgResponseTime: "2.1s",
                status: "active",
                cost: "$12.89",
              },
              {
                id: "5",
                name: "Slack Notifications",
                description: "Send messages to Slack channels",
                category: "communication",
                executions: 789,
                avgResponseTime: "0.7s",
                status: "active",
                cost: "$4.56",
              },
              {
                id: "6",
                name: "Weather API",
                description: "Get current weather and forecasts",
                category: "api",
                executions: 234,
                avgResponseTime: "1.5s",
                status: "draft",
                cost: "$1.23",
              },
            ].map((tool) => (
              <Card
                key={tool.id}
                className="hover:shadow-md transition-shadow cursor-pointer"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Wrench className="h-5 w-5" />
                      {tool.name}
                    </CardTitle>
                    <div
                      className={`px-2 py-1 rounded-full text-xs ${
                        tool.status === "active"
                          ? "bg-green-100 text-green-700"
                          : "bg-yellow-100 text-yellow-700"
                      }`}
                    >
                      {tool.status}
                    </div>
                  </div>
                  <CardDescription>{tool.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Executions</div>
                      <div className="font-medium">
                        {tool.executions.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Avg. Time</div>
                      <div className="font-medium">{tool.avgResponseTime}</div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Category</div>
                      <div className="font-medium capitalize">
                        {tool.category}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Cost</div>
                      <div className="font-medium">{tool.cost}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="api">
          <div className="text-center py-8 text-muted-foreground">
            API tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="database">
          <div className="text-center py-8 text-muted-foreground">
            Database tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="email">
          <div className="text-center py-8 text-muted-foreground">
            Email tools will be displayed here.
          </div>
        </TabsContent>

        <TabsContent value="file">
          <div className="text-center py-8 text-muted-foreground">
            File processing tools will be displayed here.
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
