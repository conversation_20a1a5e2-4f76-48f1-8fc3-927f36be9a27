import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Filter, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import AgentList from "@/components/agents/AgentList";
import AgentBuilder from "@/components/agents/AgentBuilder";

export default function AgentsPage() {
  return (
    <div className="bg-background min-h-screen p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agents</h1>
          <p className="text-muted-foreground mt-1">
            Create, manage, and monitor your AI agents
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Agent
        </Button>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="all">All Agents</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
            <TabsTrigger value="archived">Archived</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search agents..."
                className="pl-8 w-[250px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Agents</CardTitle>
                <CardDescription>Across all statuses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">24</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Active Agents</CardTitle>
                <CardDescription>Currently in production</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">18</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Executions</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">1,284</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Avg. Response Time</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">1.2s</div>
              </CardContent>
            </Card>
          </div>

          <AgentList
            agents={[
              {
                id: "1",
                name: "Customer Support Agent",
                description: "Handles customer inquiries and support tickets",
                status: "active",
                version: "1.2.0",
                lastExecuted: "2023-06-15T10:30:00Z",
                executionCount: 523,
                avgResponseTime: "1.1s",
                cost: "$12.45",
              },
              {
                id: "2",
                name: "Sales Assistant",
                description: "Helps qualify leads and answer product questions",
                status: "active",
                version: "2.0.1",
                lastExecuted: "2023-06-14T16:45:00Z",
                executionCount: 342,
                avgResponseTime: "0.9s",
                cost: "$8.72",
              },
              {
                id: "3",
                name: "Data Analyst",
                description: "Analyzes data and generates reports",
                status: "draft",
                version: "0.5.0",
                lastExecuted: "2023-06-10T09:15:00Z",
                executionCount: 12,
                avgResponseTime: "2.3s",
                cost: "$1.18",
              },
              {
                id: "4",
                name: "Content Generator",
                description: "Creates blog posts and social media content",
                status: "active",
                version: "1.0.0",
                lastExecuted: "2023-06-15T08:20:00Z",
                executionCount: 187,
                avgResponseTime: "1.8s",
                cost: "$5.63",
              },
              {
                id: "5",
                name: "Code Assistant",
                description: "Helps with code generation and debugging",
                status: "archived",
                version: "0.9.2",
                lastExecuted: "2023-05-28T14:10:00Z",
                executionCount: 220,
                avgResponseTime: "1.5s",
                cost: "$7.91",
              },
            ]}
          />
        </TabsContent>

        <TabsContent value="active">
          <AgentList
            agents={[
              {
                id: "1",
                name: "Customer Support Agent",
                description: "Handles customer inquiries and support tickets",
                status: "active",
                version: "1.2.0",
                lastExecuted: "2023-06-15T10:30:00Z",
                executionCount: 523,
                avgResponseTime: "1.1s",
                cost: "$12.45",
              },
              {
                id: "2",
                name: "Sales Assistant",
                description: "Helps qualify leads and answer product questions",
                status: "active",
                version: "2.0.1",
                lastExecuted: "2023-06-14T16:45:00Z",
                executionCount: 342,
                avgResponseTime: "0.9s",
                cost: "$8.72",
              },
              {
                id: "4",
                name: "Content Generator",
                description: "Creates blog posts and social media content",
                status: "active",
                version: "1.0.0",
                lastExecuted: "2023-06-15T08:20:00Z",
                executionCount: 187,
                avgResponseTime: "1.8s",
                cost: "$5.63",
              },
            ]}
          />
        </TabsContent>

        <TabsContent value="draft">
          <AgentList
            agents={[
              {
                id: "3",
                name: "Data Analyst",
                description: "Analyzes data and generates reports",
                status: "draft",
                version: "0.5.0",
                lastExecuted: "2023-06-10T09:15:00Z",
                executionCount: 12,
                avgResponseTime: "2.3s",
                cost: "$1.18",
              },
            ]}
          />
        </TabsContent>

        <TabsContent value="archived">
          <AgentList
            agents={[
              {
                id: "5",
                name: "Code Assistant",
                description: "Helps with code generation and debugging",
                status: "archived",
                version: "0.9.2",
                lastExecuted: "2023-05-28T14:10:00Z",
                executionCount: 220,
                avgResponseTime: "1.5s",
                cost: "$7.91",
              },
            ]}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
