declare module 'reactflow' {
  import * as React from 'react';

  // Node Types
  export type NodeTypes = Record<string, React.ComponentType<NodeProps<any>>>;
  
  // Edge Types
  export type EdgeTypes = Record<string, React.ComponentType<EdgeProps<any>>>;
  
  // Node Props
  export interface NodeProps<T = any> {
    id: string;
    type?: string;
    data: T;
    selected?: boolean;
    isConnectable?: boolean;
    xPos?: number;
    yPos?: number;
    dragging?: boolean;
    zIndex?: number;
    targetPosition?: Position;
    sourcePosition?: Position;
  }
  
  // Edge Props
  export interface EdgeProps<T = any> {
    id: string;
    source: string;
    target: string;
    sourceX: number;
    sourceY: number;
    targetX: number;
    targetY: number;
    sourcePosition: Position;
    targetPosition: Position;
    style?: React.CSSProperties;
    data?: T;
    markerEnd?: string;
    markerStart?: string;
    selected?: boolean;
  }
  
  // Node
  export interface Node<T = any> {
    id: string;
    position: XYPosition;
    data: T;
    type?: string;
    style?: React.CSSProperties;
    className?: string;
    targetPosition?: Position;
    sourcePosition?: Position;
    hidden?: boolean;
    selected?: boolean;
    dragging?: boolean;
    draggable?: boolean;
    selectable?: boolean;
    connectable?: boolean;
    dragHandle?: string;
    width?: number;
    height?: number;
    parentNode?: string;
    zIndex?: number;
    extent?: 'parent' | [number, number, number, number];
    expandParent?: boolean;
  }
  
  // Edge
  export interface Edge<T = any> {
    id: string;
    source: string;
    target: string;
    sourceHandle?: string | null;
    targetHandle?: string | null;
    type?: string;
    animated?: boolean;
    style?: React.CSSProperties;
    data?: T;
    className?: string;
    hidden?: boolean;
    selected?: boolean;
    deletable?: boolean;
    updatable?: boolean;
    interactionWidth?: number;
    zIndex?: number;
    markerEnd?: string;
    markerStart?: string;
  }
  
  // Connection
  export interface Connection {
    source: string;
    target: string;
    sourceHandle?: string | null;
    targetHandle?: string | null;
  }
  
  // Position
  export enum Position {
    Left = 'left',
    Top = 'top',
    Right = 'right',
    Bottom = 'bottom',
  }
  
  // XY Position
  export interface XYPosition {
    x: number;
    y: number;
  }
  
  // Viewport
  export interface Viewport {
    x: number;
    y: number;
    zoom: number;
  }
  
  // React Flow Props
  export interface ReactFlowProps {
    nodes: Node[];
    edges: Edge[];
    defaultNodes?: Node[];
    defaultEdges?: Edge[];
    onNodesChange?: (changes: any[]) => void;
    onEdgesChange?: (changes: any[]) => void;
    onConnect?: (connection: Connection) => void;
    onNodeClick?: (event: React.MouseEvent, node: Node) => void;
    onNodeDoubleClick?: (event: React.MouseEvent, node: Node) => void;
    onNodeMouseEnter?: (event: React.MouseEvent, node: Node) => void;
    onNodeMouseMove?: (event: React.MouseEvent, node: Node) => void;
    onNodeMouseLeave?: (event: React.MouseEvent, node: Node) => void;
    onNodeContextMenu?: (event: React.MouseEvent, node: Node) => void;
    onNodeDragStart?: (event: React.MouseEvent, node: Node) => void;
    onNodeDrag?: (event: React.MouseEvent, node: Node) => void;
    onNodeDragStop?: (event: React.MouseEvent, node: Node) => void;
    onEdgeClick?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeDoubleClick?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeMouseEnter?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeMouseMove?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeMouseLeave?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeContextMenu?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeUpdate?: (oldEdge: Edge, newConnection: Connection) => void;
    onEdgeUpdateStart?: (event: React.MouseEvent, edge: Edge) => void;
    onEdgeUpdateEnd?: (event: React.MouseEvent, edge: Edge) => void;
    nodeTypes?: NodeTypes;
    edgeTypes?: EdgeTypes;
    connectionMode?: ConnectionMode;
    connectionLineType?: ConnectionLineType;
    connectionLineStyle?: React.CSSProperties;
    connectionLineComponent?: React.ComponentType<ConnectionLineComponentProps>;
    deleteKeyCode?: string | null;
    selectionKeyCode?: string | null;
    multiSelectionKeyCode?: string | null;
    zoomActivationKeyCode?: string | null;
    snapToGrid?: boolean;
    snapGrid?: [number, number];
    onlyRenderVisibleElements?: boolean;
    defaultZoom?: number;
    defaultPosition?: [number, number];
    defaultMarkerColor?: string;
    zoomOnScroll?: boolean;
    zoomOnPinch?: boolean;
    panOnScroll?: boolean;
    panOnScrollMode?: PanOnScrollMode;
    panOnScrollSpeed?: number;
    panOnDrag?: boolean;
    preventScrolling?: boolean;
    nodesDraggable?: boolean;
    nodesConnectable?: boolean;
    elementsSelectable?: boolean;
    selectNodesOnDrag?: boolean;
    fitView?: boolean;
    fitViewOptions?: FitViewOptions;
    connectOnClick?: boolean;
    attributionPosition?: AttributionPosition;
    maxZoom?: number;
    minZoom?: number;
    children?: React.ReactNode;
  }
  
  // Connection Line Component Props
  export interface ConnectionLineComponentProps {
    sourceX: number;
    sourceY: number;
    sourcePosition: Position;
    targetX: number;
    targetY: number;
    targetPosition: Position;
    connectionLineStyle?: React.CSSProperties;
    connectionLineType: ConnectionLineType;
  }
  
  // Connection Mode
  export enum ConnectionMode {
    Strict = 'strict',
    Loose = 'loose',
  }
  
  // Connection Line Type
  export enum ConnectionLineType {
    Bezier = 'bezier',
    Straight = 'straight',
    Step = 'step',
    SmoothStep = 'smoothstep',
  }
  
  // Pan On Scroll Mode
  export enum PanOnScrollMode {
    Free = 'free',
    Horizontal = 'horizontal',
    Vertical = 'vertical',
  }
  
  // Attribution Position
  export enum AttributionPosition {
    TopLeft = 'top-left',
    TopCenter = 'top-center',
    TopRight = 'top-right',
    BottomLeft = 'bottom-left',
    BottomCenter = 'bottom-center',
    BottomRight = 'bottom-right',
  }
  
  // Fit View Options
  export interface FitViewOptions {
    padding?: number;
    includeHiddenNodes?: boolean;
    minZoom?: number;
    maxZoom?: number;
    duration?: number;
  }
  
  // Hooks
  export function useNodesState(initialNodes: Node[]): [Node[], React.Dispatch<React.SetStateAction<Node[]>>, (changes: any[]) => void];
  export function useEdgesState(initialEdges: Edge[]): [Edge[], React.Dispatch<React.SetStateAction<Edge[]>>, (changes: any[]) => void];
  export function useReactFlow(): ReactFlowInstance;
  
  // React Flow Instance
  export interface ReactFlowInstance {
    getNodes: () => Node[];
    getEdges: () => Edge[];
    getNode: (id: string) => Node | undefined;
    getEdge: (id: string) => Edge | undefined;
    setNodes: (nodes: Node[] | ((nodes: Node[]) => Node[])) => void;
    setEdges: (edges: Edge[] | ((edges: Edge[]) => Edge[])) => void;
    addNodes: (nodes: Node[]) => void;
    addEdges: (edges: Edge[]) => void;
    toObject: () => { nodes: Node[]; edges: Edge[]; viewport: Viewport };
    getViewport: () => Viewport;
    setViewport: (viewport: Viewport) => void;
    fitView: (options?: FitViewOptions) => void;
    zoomIn: (options?: { duration?: number }) => void;
    zoomOut: (options?: { duration?: number }) => void;
    zoomTo: (zoomLevel: number, options?: { duration?: number }) => void;
    getElements: () => { nodes: Node[]; edges: Edge[] };
    project: (position: XYPosition) => XYPosition;
    viewportInitialized: boolean;
  }
  
  // Components
  export const ReactFlow: React.ComponentType<ReactFlowProps>;
  export const Background: React.ComponentType<any>;
  export const MiniMap: React.ComponentType<any>;
  export const Controls: React.ComponentType<any>;
  export const Panel: React.ComponentType<any>;
  export const ReactFlowProvider: React.ComponentType<any>;
  export const Handle: React.ComponentType<any>;
  export const EdgeLabelRenderer: React.ComponentType<any>;
  
  // Utilities
  export function addEdge(edge: Edge | Connection, edges: Edge[]): Edge[];
  export function getBezierPath(params: any): [string, number, number];
  export function getSmoothStepPath(params: any): [string, number, number];
  export function getMarkerEnd(params: any): string;
}

declare module 'reactflow/dist/style.css';
